2025-01-23 12:48:13,511 DEBUG cd frappe-bench && python3 -m venv env
2025-01-23 12:48:18,676 DEBUG cd frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-01-23 12:48:22,031 DEBUG cd frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet wheel
2025-01-23 12:48:25,005 LOG Getting frappe
2025-01-23 12:48:25,005 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-01-23 12:48:44,524 LOG Installing frappe
2025-01-23 12:48:44,524 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-01-23 12:49:44,182 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-01-23 12:50:00,623 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-23 12:50:00,859 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-23 12:50:00,859 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-23 12:50:00,859 DEBUG cd frappe-bench && bench build
2025-01-23 12:50:01,087 INFO /home/<USER>/.local/bin/bench build
2025-01-23 12:50:27,653 LOG setting up backups
2025-01-23 12:50:27,661 LOG backups were set up
2025-01-23 12:50:27,661 INFO Bench frappe-bench initialized
2025-01-23 12:56:07,525 INFO /home/<USER>/.local/bin/bench new-site explore
2025-01-23 12:58:35,821 INFO /home/<USER>/.local/bin/bench get-app payments
2025-01-23 12:58:37,079 LOG Getting payments
2025-01-23 12:58:37,080 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git  --depth 1 --origin upstream
2025-01-23 12:58:38,779 LOG Installing payments
2025-01-23 12:58:38,780 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payments 
2025-01-23 12:58:47,284 DEBUG bench build --app payments
2025-01-23 12:58:47,530 INFO /home/<USER>/.local/bin/bench build --app payments
2025-01-23 12:58:50,722 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-23 12:58:51,044 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-23 12:58:51,044 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-23 12:59:14,697 INFO /home/<USER>/.local/bin/bench get-app --branch version-15 erpnext
2025-01-23 12:59:15,746 LOG Getting erpnext
2025-01-23 12:59:15,746 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-01-23 12:59:27,896 LOG Installing erpnext
2025-01-23 12:59:27,896 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext 
2025-01-23 12:59:38,457 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && yarn install --check-files
2025-01-23 12:59:39,027 DEBUG bench build --app erpnext
2025-01-23 12:59:39,300 INFO /home/<USER>/.local/bin/bench build --app erpnext
2025-01-23 12:59:44,185 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-23 12:59:44,486 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-23 12:59:44,486 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-23 13:00:12,688 INFO /home/<USER>/.local/bin/bench --site explore add-to-hosts
2025-01-23 13:00:25,487 INFO /home/<USER>/.local/bin/bench --site explore install-app erpnext
2025-01-23 13:03:10,356 INFO /home/<USER>/.local/bin/bench get-app hrms
2025-01-23 13:03:11,504 LOG Getting hrms
2025-01-23 13:03:11,504 DEBUG cd ./apps && git clone https://github.com/frappe/hrms.git  --depth 1 --origin upstream
2025-01-23 13:03:18,525 LOG Installing hrms
2025-01-23 13:03:18,525 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-01-23 13:03:21,508 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-01-23 13:03:58,431 DEBUG bench build --app hrms
2025-01-23 13:03:58,677 INFO /home/<USER>/.local/bin/bench build --app hrms
2025-01-23 13:04:33,504 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-23 13:04:33,753 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-23 13:04:33,753 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-23 13:05:32,867 INFO /home/<USER>/.local/bin/bench --site explore install-app payments
2025-01-23 13:06:12,786 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/csf_tz.git
2025-01-23 13:06:12,796 LOG Getting csf_tz
2025-01-23 13:06:12,796 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/csf_tz.git  --depth 1 --origin upstream
2025-01-23 13:06:15,596 LOG Installing csf_tz
2025-01-23 13:06:15,596 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-01-23 13:06:22,332 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-01-23 13:06:23,850 DEBUG bench build --app csf_tz
2025-01-23 13:06:24,089 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-01-23 13:06:27,335 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-23 13:06:27,620 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-23 13:06:27,620 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-23 13:06:49,616 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/icd_tz.git
2025-01-23 13:06:49,626 LOG Getting icd_tz
2025-01-23 13:06:49,626 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/icd_tz.git  --depth 1 --origin upstream
2025-01-23 13:06:51,147 LOG Installing icd_tz
2025-01-23 13:06:51,147 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/icd_tz 
2025-01-23 13:06:53,649 DEBUG bench build --app icd_tz
2025-01-23 13:06:53,857 INFO /home/<USER>/.local/bin/bench build --app icd_tz
2025-01-23 13:06:56,530 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-23 13:06:56,800 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-23 13:06:56,801 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-23 13:07:11,150 INFO /home/<USER>/.local/bin/bench start
2025-01-23 13:07:11,632 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 13:07:11,690 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 13:07:11,818 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 13:07:11,887 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 13:10:59,393 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms
2025-01-23 13:12:27,485 INFO /home/<USER>/.local/bin/bench --site explore install-app csf_tz
2025-01-23 13:15:33,686 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-01-23 13:19:08,017 INFO /home/<USER>/.local/bin/bench start
2025-01-23 13:19:08,626 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 13:19:08,650 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 13:19:08,735 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 13:19:08,770 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 13:40:26,111 INFO /home/<USER>/.local/bin/bench --site explore restore 20250121_142722-icd-dev_aakvaerp_com-database.sql
2025-01-23 14:04:26,131 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-23 14:16:26,336 INFO /home/<USER>/.local/bin/bench --site explore mariadb
2025-01-23 14:17:39,875 INFO /home/<USER>/.local/bin/bench start
2025-01-23 14:17:40,395 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 14:17:40,440 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 14:17:40,495 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 14:17:40,512 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 14:25:03,675 INFO /home/<USER>/.local/bin/bench --site explore mariadb
2025-01-23 14:41:02,277 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-23 14:41:06,843 INFO /home/<USER>/.local/bin/bench start
2025-01-23 14:41:07,323 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 14:41:07,349 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 14:41:07,416 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 14:41:07,428 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 14:45:11,885 INFO /home/<USER>/.local/bin/bench --site explore restore /home/<USER>/Desktop/frappe-bench/20250121_142722-icd-dev_aakvaerp_com-database.sql
2025-01-23 15:03:19,226 INFO /home/<USER>/.local/bin/bench new-site my-site
2025-01-23 15:10:36,301 INFO /home/<USER>/.local/bin/bench --site my-site add-to-hosts
2025-01-23 15:13:48,942 INFO /home/<USER>/.local/bin/bench --site my-site install-app erpnext
2025-01-23 15:16:45,942 INFO /home/<USER>/.local/bin/bench start
2025-01-23 15:18:49,710 INFO /home/<USER>/.local/bin/bench start
2025-01-23 15:18:50,285 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 15:18:50,366 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 15:18:50,497 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 15:18:50,512 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 15:21:48,158 INFO /home/<USER>/.local/bin/bench --site my-site install-app payments
2025-01-23 15:24:34,377 INFO /home/<USER>/.local/bin/bench --site my-site install-app hrms
2025-01-23 15:28:19,810 INFO /home/<USER>/.local/bin/bench --site my-site install-app csf_tz
2025-01-23 15:29:47,232 INFO /home/<USER>/.local/bin/bench --site my-site install-app icd_tz
2025-01-23 15:31:04,051 INFO /home/<USER>/.local/bin/bench start
2025-01-23 15:31:04,600 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 15:31:04,688 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 15:31:04,767 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 15:31:04,774 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 16:32:22,283 INFO /home/<USER>/.local/bin/bench --site explore restart
2025-01-23 16:32:22,289 WARNING /home/<USER>/.local/bin/bench --site explore restart executed with exit code 2
2025-01-23 16:34:21,782 INFO /home/<USER>/.local/bin/bench --help
2025-01-23 16:34:44,163 INFO /home/<USER>/.local/bin/bench --site explore reinstall
2025-01-23 16:53:43,874 INFO /home/<USER>/.local/bin/bench --site explore reinstall
2025-01-23 16:55:48,808 INFO /home/<USER>/.local/bin/bench --site my-site reinstall
2025-01-23 16:56:36,053 INFO /home/<USER>/.local/bin/bench --site explore install-app erpnext
2025-01-23 16:58:51,691 INFO /home/<USER>/.local/bin/bench --site explore add-to-hosts
2025-01-23 17:00:07,794 INFO /home/<USER>/.local/bin/bench --site my-site install-app erpnext
2025-01-23 17:00:28,592 INFO /home/<USER>/.local/bin/bench start
2025-01-23 17:00:29,263 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 17:00:29,294 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 17:00:29,356 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 17:00:29,368 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 17:04:26,710 INFO /home/<USER>/.local/bin/bench --site explore install-app payments
2025-01-23 17:04:34,274 INFO /home/<USER>/.local/bin/bench --site my-site add-to-hosts
2025-01-23 17:05:09,720 INFO /home/<USER>/.local/bin/bench start
2025-01-23 17:05:10,313 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 17:05:10,325 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 17:05:10,459 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 17:05:10,505 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 17:17:11,337 INFO /home/<USER>/.local/bin/bench --site explore install-app payments
2025-01-23 17:17:57,796 INFO /home/<USER>/.local/bin/bench start
2025-01-23 17:17:58,371 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 17:17:58,437 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 17:17:58,522 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 17:17:58,562 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 17:19:48,633 INFO /home/<USER>/.local/bin/bench --site my-site install-app payments
2025-01-23 17:20:27,972 INFO /home/<USER>/.local/bin/bench --site my-site install-app https://github.com/frappe/hrms.git
2025-01-23 17:21:31,152 INFO /home/<USER>/.local/bin/bench --site my-site install-app hrms
2025-01-23 17:21:43,318 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms
2025-01-23 17:29:19,558 INFO /home/<USER>/.local/bin/bench --site my-site install-app https://github.com/Aakvatech-Limited/csf_tz.git
2025-01-23 17:29:51,383 INFO /home/<USER>/.local/bin/bench --site my-site install-app csf_tz
2025-01-23 17:30:57,764 INFO /home/<USER>/.local/bin/bench --site explore install-app csf_tz
2025-01-23 17:32:20,244 INFO /home/<USER>/.local/bin/bench --site my-site install-app icd_tz
2025-01-23 17:32:35,892 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-01-23 17:32:53,198 INFO /home/<USER>/.local/bin/bench start
2025-01-23 17:32:53,720 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 17:32:53,726 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 17:32:53,798 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 17:32:53,839 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 17:54:49,490 INFO /home/<USER>/.local/bin/bench start
2025-01-23 17:54:50,216 INFO /home/<USER>/.local/bin/bench worker
2025-01-23 17:54:50,380 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 17:54:50,436 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 17:54:50,441 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 18:00:01,934 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-23 18:15:47,012 INFO /home/<USER>/.local/bin/bench start
2025-01-23 18:15:47,607 INFO /home/<USER>/.local/bin/bench watch
2025-01-23 18:15:47,799 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-23 18:15:47,809 INFO /home/<USER>/.local/bin/bench schedule
2025-01-23 18:15:47,819 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 08:37:44,928 INFO /home/<USER>/.local/bin/bench start
2025-01-24 08:37:45,529 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 08:37:45,559 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 08:37:45,573 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 08:37:45,645 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 09:17:34,423 INFO /home/<USER>/.local/bin/bench start
2025-01-24 09:17:35,012 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 09:17:35,042 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 09:17:35,083 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 09:17:35,106 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 10:51:31,176 INFO /home/<USER>/.local/bin/bench start
2025-01-24 10:51:31,863 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 10:51:32,003 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 10:51:32,022 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 10:51:32,088 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 10:56:04,325 INFO /home/<USER>/.local/bin/bench start
2025-01-24 10:56:04,929 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 10:56:04,932 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 10:56:04,997 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 10:56:05,070 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 11:39:40,244 INFO /home/<USER>/.local/bin/bench --site explore mariadb
2025-01-24 11:47:17,035 INFO /home/<USER>/.local/bin/bench start
2025-01-24 11:47:17,598 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 11:47:17,648 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 11:47:17,671 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 11:47:17,710 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 11:50:11,208 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 11:54:07,330 INFO /home/<USER>/.local/bin/bench start
2025-01-24 11:54:07,864 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 11:54:07,906 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 11:54:07,985 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 11:54:08,014 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 11:54:30,174 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 12:00:01,965 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-24 12:11:41,328 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-24 12:11:42,615 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-24 12:12:02,699 INFO /home/<USER>/.local/bin/bench start
2025-01-24 12:12:03,274 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 12:12:03,313 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 12:12:03,329 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 12:12:03,383 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 12:15:58,439 INFO /home/<USER>/.local/bin/bench --site explore set-admin-password aakvatech
2025-01-24 12:16:53,639 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 12:20:11,044 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 12:21:33,553 INFO /home/<USER>/.local/bin/bench start
2025-01-24 12:21:34,053 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 12:21:34,178 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 12:21:34,248 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 12:21:34,322 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 12:27:24,855 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 12:30:53,336 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-24 12:30:59,771 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-24 12:31:19,620 INFO /home/<USER>/.local/bin/bench --site explore set-admin-password aakvatech
2025-01-24 12:31:31,368 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 12:40:47,131 INFO /home/<USER>/.local/bin/bench --site my-site migrate
2025-01-24 12:45:19,524 INFO /home/<USER>/.local/bin/bench --site my-site set-admin-password aakvatech
2025-01-24 12:45:27,024 INFO /home/<USER>/.local/bin/bench --site my-site migrate
2025-01-24 12:54:39,959 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git
2025-01-24 12:54:39,970 LOG Getting wcfcb_zm
2025-01-24 12:54:39,970 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-01-24 12:55:24,567 WARNING cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream executed with exit code 128
2025-01-24 12:55:24,572 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git executed with exit code 1
2025-01-24 13:01:14,058 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git
2025-01-24 13:01:14,069 LOG Getting wcfcb_zm
2025-01-24 13:01:14,070 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-01-24 13:02:04,362 WARNING cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream executed with exit code 128
2025-01-24 13:02:04,364 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git executed with exit code 1
2025-01-24 13:07:38,669 INFO /home/<USER>/.local/bin/bench get-app https://Emmafidelis:<EMAIL>/Emmafidelis/wcfcb_zm.git
2025-01-24 13:07:38,680 LOG Getting wcfcb_zm
2025-01-24 13:07:38,680 DEBUG cd ./apps && git clone https://Emmafidelis:<EMAIL>/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-01-24 13:07:40,705 LOG Installing wcfcb_zm
2025-01-24 13:07:40,706 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm 
2025-01-24 13:07:43,922 DEBUG bench build --app wcfcb_zm
2025-01-24 13:07:44,153 INFO /home/<USER>/.local/bin/bench build --app wcfcb_zm
2025-01-24 13:08:04,575 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-24 13:08:04,852 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-24 13:08:04,852 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-24 13:45:58,277 INFO /home/<USER>/.local/bin/bench new-site wcfcb-zm.aakvaerp.com
2025-01-24 13:50:18,438 INFO /home/<USER>/.local/bin/bench -site wcfcb-zm.aakvaerp.com install-app erpnext
2025-01-24 13:50:41,869 INFO /home/<USER>/.local/bin/bench --site wcfcb-zm.aakvaerp.com install-app erpnext
2025-01-24 13:52:43,053 INFO /home/<USER>/.local/bin/bench --site wcfcb-zm.aakvaerp.com add-to-hosts
2025-01-24 13:53:00,668 INFO /home/<USER>/.local/bin/bench start
2025-01-24 13:53:01,222 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 13:53:01,339 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 13:53:01,359 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 13:53:01,496 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 13:54:36,023 INFO /home/<USER>/.local/bin/bench use wcfcb-zm.aakvaerp.com add-to-hosts
2025-01-24 13:54:44,718 INFO /home/<USER>/.local/bin/bench use wcfcb-zm.aakvaerp.com
2025-01-24 13:54:47,643 INFO /home/<USER>/.local/bin/bench start
2025-01-24 13:54:48,187 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 13:54:48,190 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 13:54:48,270 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 13:54:48,364 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 13:57:44,732 INFO /home/<USER>/.local/bin/bench start
2025-01-24 13:57:45,272 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 13:57:45,319 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 13:57:45,333 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 13:57:45,337 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 14:01:23,248 INFO /home/<USER>/.local/bin/bench --site wcfcb-zm.aakvaerp.com install-app wcfcb_zm
2025-01-24 14:02:17,548 INFO /home/<USER>/.local/bin/bench --site wcfcb-zm.aakvaerp.com install-app hrms
2025-01-24 14:03:30,152 INFO /home/<USER>/.local/bin/bench start
2025-01-24 14:03:30,677 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 14:03:30,750 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 14:03:30,835 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 14:03:30,863 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 14:17:21,362 INFO /home/<USER>/.local/bin/bench set-config -g developer_mode true
2025-01-24 14:17:29,373 INFO /home/<USER>/.local/bin/bench start
2025-01-24 14:17:29,871 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 14:17:29,916 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 14:17:29,974 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 14:17:29,998 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 14:42:58,685 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git
2025-01-24 14:43:04,834 INFO App moved from apps/wcfcb_zm to archived/apps/wcfcb_zm-2025-01-24
2025-01-24 14:43:04,839 LOG Getting wcfcb_zm
2025-01-24 14:43:04,839 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-01-24 14:43:18,404 WARNING cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream executed with exit code 128
2025-01-24 14:43:18,405 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git executed with exit code 1
2025-01-24 14:45:37,164 INFO /home/<USER>/.local/bin/bench get-app https://Emmafidelis:<EMAIL>/Emmafidelis/wcfcb_zm.git
2025-01-24 14:45:37,175 LOG Getting wcfcb_zm
2025-01-24 14:45:37,175 DEBUG cd ./apps && git clone https://Emmafidelis:<EMAIL>/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-01-24 14:45:39,005 LOG Installing wcfcb_zm
2025-01-24 14:45:39,005 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm 
2025-01-24 14:45:42,903 DEBUG bench build --app wcfcb_zm
2025-01-24 14:45:43,130 INFO /home/<USER>/.local/bin/bench build --app wcfcb_zm
2025-01-24 14:45:54,092 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-24 14:45:54,369 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-24 14:45:54,370 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-24 14:47:25,271 INFO /home/<USER>/.local/bin/bench new-site working
2025-01-24 14:56:02,306 INFO /home/<USER>/.local/bin/bench --site working add-to-hosts
2025-01-24 14:56:26,299 INFO /home/<USER>/.local/bin/bench --site install-app erpnext
2025-01-24 14:56:42,519 INFO /home/<USER>/.local/bin/bench --site working install-app erpnext
2025-01-24 14:58:22,499 INFO /home/<USER>/.local/bin/bench start
2025-01-24 14:58:22,994 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 14:58:23,046 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 14:58:23,287 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 14:58:23,415 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 15:16:45,238 INFO /home/<USER>/.local/bin/bench get-app --branch version-14 https://github.com/Aakvatech-Limited/csf_tz.git
2025-01-24 15:16:45,258 LOG Getting csf_tz
2025-01-24 15:16:45,258 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/csf_tz.git --branch version-14 --depth 1 --origin upstream
2025-01-24 15:16:48,078 LOG Installing csf_tz
2025-01-24 15:16:48,079 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-01-24 15:16:56,541 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-01-24 15:17:00,546 DEBUG bench build --app csf_tz
2025-01-24 15:17:00,851 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-01-24 15:17:12,455 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-24 15:17:12,750 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-24 15:17:12,750 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-24 15:18:49,728 INFO /home/<USER>/.local/bin/bench --site working install-app csf_tz
2025-01-24 15:20:00,432 INFO /home/<USER>/.local/bin/bench start
2025-01-24 15:20:01,027 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 15:20:01,122 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 15:20:01,150 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 15:20:01,245 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 15:24:22,631 INFO /home/<USER>/.local/bin/bench --site working install-app hrms
2025-01-24 15:27:35,554 INFO /home/<USER>/.local/bin/bench --site working install-app csf_tz --force
2025-01-24 15:29:14,210 INFO /home/<USER>/.local/bin/bench start
2025-01-24 15:29:14,810 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 15:29:14,838 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 15:29:14,845 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 15:29:14,887 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 15:30:55,195 INFO /home/<USER>/.local/bin/bench --site working install-app csf_tz
2025-01-24 15:31:19,035 INFO /home/<USER>/.local/bin/bench use working
2025-01-24 15:31:22,913 INFO /home/<USER>/.local/bin/bench start
2025-01-24 15:31:23,468 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 15:31:23,478 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 15:31:23,573 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 15:31:23,679 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 15:34:27,035 INFO /home/<USER>/.local/bin/bench --site working install-app wcfcb_zm
2025-01-24 15:34:38,788 INFO /home/<USER>/.local/bin/bench start
2025-01-24 15:34:39,326 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 15:34:39,370 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 15:34:39,383 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 15:34:39,405 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 16:32:29,335 INFO /home/<USER>/.local/bin/bench use explore
2025-01-24 16:32:47,377 INFO /home/<USER>/.local/bin/bench start
2025-01-24 16:32:47,930 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 16:32:47,959 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 16:32:47,994 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 16:32:48,050 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 17:11:49,891 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/icd_tz.git
2025-01-24 17:11:49,902 LOG Getting icd_tz
2025-01-24 17:11:49,902 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/icd_tz.git  --depth 1 --origin upstream
2025-01-24 17:11:51,866 LOG Installing icd_tz
2025-01-24 17:11:51,867 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/icd_tz 
2025-01-24 17:11:55,372 DEBUG bench build --app icd_tz
2025-01-24 17:11:55,671 INFO /home/<USER>/.local/bin/bench build --app icd_tz
2025-01-24 17:12:05,749 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-24 17:12:06,017 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-24 17:12:06,018 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-24 17:13:02,090 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-01-24 17:13:09,754 INFO /home/<USER>/.local/bin/bench start
2025-01-24 17:13:10,318 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 17:13:10,375 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 17:13:10,493 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 17:13:10,501 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 17:14:01,715 INFO /home/<USER>/.local/bin/bench start
2025-01-24 17:14:02,326 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-24 17:14:02,414 INFO /home/<USER>/.local/bin/bench watch
2025-01-24 17:14:02,422 INFO /home/<USER>/.local/bin/bench worker
2025-01-24 17:14:02,632 INFO /home/<USER>/.local/bin/bench schedule
2025-01-24 17:14:27,046 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-24 17:14:41,346 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-24 17:14:53,694 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-01-24 18:00:01,616 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-25 08:06:22,923 INFO /home/<USER>/.local/bin/bench use explore
2025-01-25 08:07:59,043 INFO /home/<USER>/.local/bin/bench start
2025-01-25 08:07:59,595 INFO /home/<USER>/.local/bin/bench worker
2025-01-25 08:07:59,611 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-25 08:07:59,629 INFO /home/<USER>/.local/bin/bench watch
2025-01-25 08:07:59,661 INFO /home/<USER>/.local/bin/bench schedule
2025-01-25 11:53:27,904 INFO /home/<USER>/.local/bin/bench --site explore console
2025-01-25 11:57:58,474 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-25 11:58:11,317 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-25 12:00:02,196 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-25 12:37:54,168 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-25 12:37:58,476 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-25 14:49:57,142 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-25 14:50:00,437 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-25 14:50:10,634 INFO /home/<USER>/.local/bin/bench start
2025-01-25 14:50:11,175 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-25 14:50:11,255 INFO /home/<USER>/.local/bin/bench schedule
2025-01-25 14:50:11,277 INFO /home/<USER>/.local/bin/bench watch
2025-01-25 14:50:11,332 INFO /home/<USER>/.local/bin/bench worker
2025-01-25 15:36:09,509 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-25 15:36:22,412 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-25 18:00:01,626 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-25 18:25:09,669 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-25 18:25:13,608 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 00:00:01,871 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-26 17:16:29,380 INFO /home/<USER>/.local/bin/bench start
2025-01-26 17:16:29,974 INFO /home/<USER>/.local/bin/bench watch
2025-01-26 17:16:29,981 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-26 17:16:30,073 INFO /home/<USER>/.local/bin/bench schedule
2025-01-26 17:16:30,139 INFO /home/<USER>/.local/bin/bench worker
2025-01-26 17:28:50,485 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-26 17:29:04,454 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 17:33:41,977 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-26 17:33:45,685 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 17:39:27,746 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-26 17:39:31,873 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 17:49:48,531 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-26 17:49:53,798 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 18:00:02,135 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-26 18:50:50,588 INFO /home/<USER>/.local/bin/bench start
2025-01-26 18:50:51,185 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-26 18:50:51,223 INFO /home/<USER>/.local/bin/bench watch
2025-01-26 18:50:51,312 INFO /home/<USER>/.local/bin/bench worker
2025-01-26 18:50:51,375 INFO /home/<USER>/.local/bin/bench schedule
2025-01-26 18:57:48,110 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-26 18:57:53,226 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 19:23:28,365 INFO /home/<USER>/.local/bin/bench --site explore mariadb
2025-01-26 19:28:08,050 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-26 19:28:14,572 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-26 19:31:08,304 INFO /home/<USER>/.local/bin/bench --site explore tail
2025-01-27 08:45:55,911 INFO /home/<USER>/.local/bin/bench start
2025-01-27 08:45:56,488 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-27 08:45:56,490 INFO /home/<USER>/.local/bin/bench schedule
2025-01-27 08:45:56,530 INFO /home/<USER>/.local/bin/bench worker
2025-01-27 08:45:56,549 INFO /home/<USER>/.local/bin/bench watch
2025-01-27 12:00:01,751 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-27 18:00:01,383 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-28 09:14:21,360 INFO /home/<USER>/.local/bin/bench start
2025-01-28 09:14:21,981 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-28 09:14:22,049 INFO /home/<USER>/.local/bin/bench watch
2025-01-28 09:14:22,162 INFO /home/<USER>/.local/bin/bench worker
2025-01-28 09:14:22,243 INFO /home/<USER>/.local/bin/bench schedule
2025-01-28 12:00:01,908 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-28 14:27:13,093 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-28 14:27:27,058 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-28 15:53:58,580 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-28 15:54:02,288 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-28 16:00:55,780 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-28 16:00:59,855 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-28 16:14:59,256 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/icd_tz.git
2025-01-28 16:14:59,269 LOG Getting icd_tz
2025-01-28 16:14:59,269 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/icd_tz.git  --depth 1 --origin upstream
2025-01-28 16:15:01,398 LOG Installing icd_tz
2025-01-28 16:15:01,398 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/icd_tz 
2025-01-28 16:15:04,646 DEBUG bench build --app icd_tz
2025-01-28 16:15:04,935 INFO /home/<USER>/.local/bin/bench build --app icd_tz
2025-01-28 16:15:16,143 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-28 16:15:16,411 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-28 16:15:16,411 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-28 16:15:56,742 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-01-28 16:16:07,895 INFO /home/<USER>/.local/bin/bench start
2025-01-28 16:16:08,467 INFO /home/<USER>/.local/bin/bench watch
2025-01-28 16:16:08,486 INFO /home/<USER>/.local/bin/bench schedule
2025-01-28 16:16:08,516 INFO /home/<USER>/.local/bin/bench worker
2025-01-28 16:16:08,529 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-28 16:37:37,885 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-28 16:37:41,357 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-28 16:54:02,550 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-28 16:54:06,203 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-28 17:20:38,810 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-01-28 17:20:42,473 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-01-28 18:00:01,942 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-29 08:49:13,699 INFO /home/<USER>/.local/bin/bench start
2025-01-29 08:49:14,250 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-29 08:49:14,309 INFO /home/<USER>/.local/bin/bench schedule
2025-01-29 08:49:14,343 INFO /home/<USER>/.local/bin/bench worker
2025-01-29 08:49:14,352 INFO /home/<USER>/.local/bin/bench watch
2025-01-29 08:57:31,599 INFO /home/<USER>/.local/bin/bench start
2025-01-29 08:57:32,152 INFO /home/<USER>/.local/bin/bench schedule
2025-01-29 08:57:32,182 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-29 08:57:32,235 INFO /home/<USER>/.local/bin/bench worker
2025-01-29 08:57:32,253 INFO /home/<USER>/.local/bin/bench watch
2025-01-29 09:22:40,970 INFO /home/<USER>/.local/bin/bench start
2025-01-29 09:22:41,651 INFO /home/<USER>/.local/bin/bench schedule
2025-01-29 09:22:41,659 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-29 09:22:41,714 INFO /home/<USER>/.local/bin/bench watch
2025-01-29 09:22:41,775 INFO /home/<USER>/.local/bin/bench worker
2025-01-29 09:42:21,789 INFO /home/<USER>/.local/bin/bench start
2025-01-29 09:42:22,365 INFO /home/<USER>/.local/bin/bench schedule
2025-01-29 09:42:22,377 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-29 09:42:22,435 INFO /home/<USER>/.local/bin/bench worker
2025-01-29 09:42:22,488 INFO /home/<USER>/.local/bin/bench watch
2025-01-29 12:00:01,515 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-29 12:53:00,367 INFO /home/<USER>/.local/bin/bench start
2025-01-29 12:53:00,964 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-29 12:53:01,051 INFO /home/<USER>/.local/bin/bench schedule
2025-01-29 12:53:01,060 INFO /home/<USER>/.local/bin/bench worker
2025-01-29 12:53:01,087 INFO /home/<USER>/.local/bin/bench watch
2025-01-29 14:00:03,262 INFO /home/<USER>/.local/bin/bench start
2025-01-29 14:00:03,884 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-29 14:00:03,939 INFO /home/<USER>/.local/bin/bench watch
2025-01-29 14:00:03,993 INFO /home/<USER>/.local/bin/bench schedule
2025-01-29 14:00:04,018 INFO /home/<USER>/.local/bin/bench worker
2025-01-29 18:00:01,449 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-30 08:53:15,846 INFO /home/<USER>/.local/bin/bench start
2025-01-30 08:53:16,466 INFO /home/<USER>/.local/bin/bench worker
2025-01-30 08:53:16,552 INFO /home/<USER>/.local/bin/bench watch
2025-01-30 08:53:16,699 INFO /home/<USER>/.local/bin/bench schedule
2025-01-30 08:53:16,720 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-30 12:00:02,308 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-30 13:03:12,639 INFO /home/<USER>/.local/bin/bench new-app school
2025-01-30 13:03:12,649 LOG creating new app school
2025-01-30 13:04:09,982 LOG Installing school
2025-01-30 13:04:09,989 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/school 
2025-01-30 13:04:13,859 DEBUG bench build --app school
2025-01-30 13:04:14,095 INFO /home/<USER>/.local/bin/bench build --app school
2025-01-30 13:04:26,066 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-01-30 13:04:26,415 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-01-30 13:04:26,415 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-01-30 13:06:44,421 INFO /home/<USER>/.local/bin/bench use working
2025-01-30 13:07:23,700 INFO /home/<USER>/.local/bin/bench --site install-app erpnext
2025-01-30 13:07:42,638 INFO /home/<USER>/.local/bin/bench --site working install-app erpnext
2025-01-30 13:08:38,506 INFO /home/<USER>/.local/bin/bench --site working install-app payments
2025-01-30 13:09:13,044 INFO /home/<USER>/.local/bin/bench --site working add-to-hosts
2025-01-30 13:09:23,691 INFO /home/<USER>/.local/bin/bench start
2025-01-30 13:09:24,239 INFO /home/<USER>/.local/bin/bench worker
2025-01-30 13:09:24,246 INFO /home/<USER>/.local/bin/bench watch
2025-01-30 13:09:24,333 INFO /home/<USER>/.local/bin/bench schedule
2025-01-30 13:09:24,448 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-30 13:11:04,623 INFO /home/<USER>/.local/bin/bench --site working install-app hrms
2025-01-30 13:11:38,081 INFO /home/<USER>/.local/bin/bench --site working install-app csf_tz
2025-01-30 18:00:01,841 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-31 08:36:07,266 INFO /home/<USER>/.local/bin/bench start
2025-01-31 08:36:07,794 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 08:36:07,887 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 08:36:07,944 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 08:36:07,951 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 10:03:38,682 INFO /home/<USER>/.local/bin/bench start
2025-01-31 10:03:39,238 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 10:03:39,401 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 10:03:39,405 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 10:03:39,457 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 10:07:44,704 INFO /home/<USER>/.local/bin/bench --site working set-config -p allow_cors *
2025-01-31 10:12:56,771 INFO /home/<USER>/.local/bin/bench --site working migrate
2025-01-31 10:13:25,695 INFO /home/<USER>/.local/bin/bench start
2025-01-31 10:13:26,374 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 10:13:26,422 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 10:13:26,450 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 10:13:26,536 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 10:16:58,523 INFO /home/<USER>/.local/bin/bench --site working migrate
2025-01-31 10:17:27,037 INFO /home/<USER>/.local/bin/bench start
2025-01-31 10:17:27,662 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 10:17:27,835 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 10:17:28,010 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 10:17:28,045 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 12:00:02,043 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-01-31 12:10:00,562 INFO /home/<USER>/.local/bin/bench start
2025-01-31 12:10:01,212 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 12:10:01,237 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 12:10:01,276 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 12:10:01,357 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 14:27:25,633 INFO /home/<USER>/.local/bin/bench start
2025-01-31 14:27:26,303 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 14:27:26,325 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 14:27:26,329 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 14:27:26,411 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 14:27:48,685 INFO /home/<USER>/.local/bin/bench --site working migrate
2025-01-31 14:29:39,852 INFO /home/<USER>/.local/bin/bench --site working install-app school
2025-01-31 14:41:49,406 INFO /home/<USER>/.local/bin/bench --site working install-app school
2025-01-31 14:44:18,224 INFO /home/<USER>/.local/bin/bench --site working execute frappe.get_all('Module Def', filters={'module_name': 'School'})
2025-01-31 14:45:20,293 INFO /home/<USER>/.local/bin/bench --site working install-app school --force
2025-01-31 14:45:58,809 INFO /home/<USER>/.local/bin/bench --site working migrate
2025-01-31 14:46:50,062 INFO /home/<USER>/.local/bin/bench start
2025-01-31 14:46:50,648 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 14:46:50,728 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 14:46:50,746 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 14:46:50,880 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 15:08:39,329 INFO /home/<USER>/.local/bin/bench start
2025-01-31 15:08:40,080 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 15:08:40,190 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 15:08:40,203 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 15:08:40,221 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 17:12:07,267 INFO /home/<USER>/.local/bin/bench start
2025-01-31 17:12:08,126 INFO /home/<USER>/.local/bin/bench schedule
2025-01-31 17:12:08,161 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-01-31 17:12:08,184 INFO /home/<USER>/.local/bin/bench watch
2025-01-31 17:12:08,262 INFO /home/<USER>/.local/bin/bench worker
2025-01-31 18:00:01,551 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-01 12:00:01,511 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-01 18:00:01,592 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-03 08:47:33,683 INFO /home/<USER>/.local/bin/bench start
2025-02-03 08:47:34,269 INFO /home/<USER>/.local/bin/bench worker
2025-02-03 08:47:34,386 INFO /home/<USER>/.local/bin/bench watch
2025-02-03 08:47:34,397 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-03 08:47:34,407 INFO /home/<USER>/.local/bin/bench schedule
2025-02-03 08:48:02,667 INFO /home/<USER>/.local/bin/bench use working
2025-02-03 12:00:01,448 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-03 12:23:13,002 INFO /home/<USER>/.local/bin/bench start
2025-02-03 12:23:13,576 INFO /home/<USER>/.local/bin/bench schedule
2025-02-03 12:23:13,643 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-03 12:23:13,699 INFO /home/<USER>/.local/bin/bench worker
2025-02-03 12:23:13,738 INFO /home/<USER>/.local/bin/bench watch
2025-02-03 13:31:42,845 INFO /home/<USER>/.local/bin/bench start
2025-02-03 13:31:43,716 INFO /home/<USER>/.local/bin/bench worker
2025-02-03 13:31:43,717 INFO /home/<USER>/.local/bin/bench watch
2025-02-03 13:31:43,844 INFO /home/<USER>/.local/bin/bench schedule
2025-02-03 13:31:43,912 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-03 15:47:17,161 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/wiki
2025-02-03 15:47:17,178 LOG Getting wiki
2025-02-03 15:47:17,178 DEBUG cd ./apps && git clone https://github.com/frappe/wiki  --depth 1 --origin upstream
2025-02-03 15:47:50,299 WARNING cd ./apps && git clone https://github.com/frappe/wiki  --depth 1 --origin upstream executed with exit code 128
2025-02-03 15:47:50,300 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/frappe/wiki executed with exit code 1
2025-02-03 15:49:45,283 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/wiki.git
2025-02-03 15:49:45,294 LOG Getting wiki
2025-02-03 15:49:45,295 DEBUG cd ./apps && git clone https://github.com/frappe/wiki.git  --depth 1 --origin upstream
2025-02-03 15:49:50,520 LOG Installing wiki
2025-02-03 15:49:50,521 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wiki 
2025-02-03 15:49:54,113 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && yarn install --check-files
2025-02-03 15:50:13,668 DEBUG bench build --app wiki
2025-02-03 15:50:13,973 INFO /home/<USER>/.local/bin/bench build --app wiki
2025-02-03 15:50:29,253 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-03 15:50:29,590 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-03 15:50:29,590 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-03 15:51:32,630 INFO /home/<USER>/.local/bin/bench use explore
2025-02-03 15:51:55,420 INFO /home/<USER>/.local/bin/bench --site explore wiki
2025-02-03 15:52:25,548 INFO /home/<USER>/.local/bin/bench --site exploreinstall-app wiki
2025-02-03 15:53:19,366 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki
2025-02-03 15:56:59,015 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki --force
2025-02-03 15:57:26,549 INFO /home/<USER>/.local/bin/bench start
2025-02-03 15:57:27,080 INFO /home/<USER>/.local/bin/bench schedule
2025-02-03 15:57:27,126 INFO /home/<USER>/.local/bin/bench watch
2025-02-03 15:57:27,302 INFO /home/<USER>/.local/bin/bench worker
2025-02-03 15:57:27,315 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-03 16:39:01,808 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-02-03 16:39:47,255 INFO /home/<USER>/.local/bin/bench start
2025-02-03 16:39:48,026 INFO /home/<USER>/.local/bin/bench schedule
2025-02-03 16:39:48,128 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-03 16:39:48,184 INFO /home/<USER>/.local/bin/bench watch
2025-02-03 16:39:48,264 INFO /home/<USER>/.local/bin/bench worker
2025-02-03 16:43:06,872 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/wiki
2025-02-03 16:43:10,426 INFO App moved from apps/wiki to archived/apps/wiki-2025-02-03
2025-02-03 16:43:10,432 LOG Getting wiki
2025-02-03 16:43:10,432 DEBUG cd ./apps && git clone https://github.com/frappe/wiki  --depth 1 --origin upstream
2025-02-03 16:43:13,764 LOG Installing wiki
2025-02-03 16:43:13,766 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wiki 
2025-02-03 16:43:19,832 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && yarn install --check-files
2025-02-03 16:43:25,241 DEBUG bench build --app wiki
2025-02-03 16:43:25,603 INFO /home/<USER>/.local/bin/bench build --app wiki
2025-02-03 16:43:43,540 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-03 16:43:43,976 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-03 16:43:43,976 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-03 16:44:28,389 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki
2025-02-03 16:44:49,551 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-03 16:44:59,831 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-03 17:04:38,069 INFO /home/<USER>/.local/bin/bench build
2025-02-03 17:11:56,974 INFO /home/<USER>/.local/bin/bench start
2025-02-03 17:11:57,864 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-03 17:11:57,946 INFO /home/<USER>/.local/bin/bench watch
2025-02-03 17:11:57,946 INFO /home/<USER>/.local/bin/bench schedule
2025-02-03 17:11:58,011 INFO /home/<USER>/.local/bin/bench worker
2025-02-03 18:00:01,670 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-04 08:50:04,111 INFO /home/<USER>/.local/bin/bench start
2025-02-04 08:50:04,627 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-04 08:50:04,755 INFO /home/<USER>/.local/bin/bench watch
2025-02-04 08:50:04,775 INFO /home/<USER>/.local/bin/bench schedule
2025-02-04 08:50:04,799 INFO /home/<USER>/.local/bin/bench worker
2025-02-04 10:02:23,466 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-02-04 11:47:19,005 INFO /home/<USER>/.local/bin/bench start
2025-02-04 11:47:19,791 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-04 11:47:19,812 INFO /home/<USER>/.local/bin/bench watch
2025-02-04 11:47:19,870 INFO /home/<USER>/.local/bin/bench schedule
2025-02-04 11:47:19,931 INFO /home/<USER>/.local/bin/bench worker
2025-02-04 12:00:01,783 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-04 13:51:20,760 INFO /home/<USER>/.local/bin/bench start
2025-02-04 13:51:21,530 INFO /home/<USER>/.local/bin/bench watch
2025-02-04 13:51:21,586 INFO /home/<USER>/.local/bin/bench schedule
2025-02-04 13:51:21,710 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-04 13:51:21,779 INFO /home/<USER>/.local/bin/bench worker
2025-02-05 08:49:53,321 INFO /home/<USER>/.local/bin/bench start
2025-02-05 08:49:53,875 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-05 08:49:54,002 INFO /home/<USER>/.local/bin/bench watch
2025-02-05 08:49:54,107 INFO /home/<USER>/.local/bin/bench worker
2025-02-05 08:49:54,119 INFO /home/<USER>/.local/bin/bench schedule
2025-02-05 12:00:01,574 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-05 16:28:20,676 INFO /home/<USER>/.local/bin/bench start
2025-02-05 16:28:21,256 INFO /home/<USER>/.local/bin/bench watch
2025-02-05 16:28:21,325 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-05 16:28:21,478 INFO /home/<USER>/.local/bin/bench worker
2025-02-05 16:28:21,495 INFO /home/<USER>/.local/bin/bench schedule
2025-02-05 16:28:58,742 INFO /home/<USER>/.local/bin/bench use working
2025-02-05 16:29:15,715 INFO /home/<USER>/.local/bin/bench start
2025-02-05 16:29:16,376 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-05 16:29:16,389 INFO /home/<USER>/.local/bin/bench schedule
2025-02-05 16:29:16,390 INFO /home/<USER>/.local/bin/bench watch
2025-02-05 16:29:16,564 INFO /home/<USER>/.local/bin/bench worker
2025-02-05 19:33:11,841 INFO /home/<USER>/.local/bin/bench start
2025-02-05 19:33:12,408 INFO /home/<USER>/.local/bin/bench worker
2025-02-05 19:33:12,460 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-05 19:33:12,464 INFO /home/<USER>/.local/bin/bench watch
2025-02-05 19:33:12,473 INFO /home/<USER>/.local/bin/bench schedule
2025-02-05 19:47:14,695 INFO /home/<USER>/.local/bin/bench start
2025-02-05 19:47:15,443 INFO /home/<USER>/.local/bin/bench watch
2025-02-05 19:47:15,566 INFO /home/<USER>/.local/bin/bench schedule
2025-02-05 19:47:15,579 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-05 19:47:15,698 INFO /home/<USER>/.local/bin/bench worker
2025-02-05 21:10:20,181 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-05 21:10:30,500 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-06 10:40:12,775 INFO /home/<USER>/.local/bin/bench use explore
2025-02-06 10:40:19,756 INFO /home/<USER>/.local/bin/bench start
2025-02-06 10:40:20,439 INFO /home/<USER>/.local/bin/bench schedule
2025-02-06 10:40:20,469 INFO /home/<USER>/.local/bin/bench watch
2025-02-06 10:40:20,607 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-06 10:40:20,610 INFO /home/<USER>/.local/bin/bench worker
2025-02-06 12:00:02,075 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-07 09:57:07,864 INFO /home/<USER>/.local/bin/bench start
2025-02-07 09:57:08,528 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-07 09:57:08,589 INFO /home/<USER>/.local/bin/bench worker
2025-02-07 09:57:08,622 INFO /home/<USER>/.local/bin/bench watch
2025-02-07 09:57:08,720 INFO /home/<USER>/.local/bin/bench schedule
2025-02-07 10:10:42,715 INFO /home/<USER>/.local/bin/bench use working
2025-02-07 10:14:50,486 INFO /home/<USER>/.local/bin/bench --site working install-app school
2025-02-07 10:17:09,090 INFO /home/<USER>/.local/bin/bench --site working migrate
2025-02-07 10:17:58,449 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-07 10:18:13,306 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-07 10:21:38,344 INFO /home/<USER>/.local/bin/bench start
2025-02-07 10:21:38,896 INFO /home/<USER>/.local/bin/bench schedule
2025-02-07 10:21:38,916 INFO /home/<USER>/.local/bin/bench watch
2025-02-07 10:21:39,048 INFO /home/<USER>/.local/bin/bench worker
2025-02-07 10:21:39,059 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-07 10:24:41,122 INFO /home/<USER>/.local/bin/bench start
2025-02-07 10:24:41,620 INFO /home/<USER>/.local/bin/bench watch
2025-02-07 10:24:41,697 INFO /home/<USER>/.local/bin/bench worker
2025-02-07 10:24:41,758 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-07 10:24:41,931 INFO /home/<USER>/.local/bin/bench schedule
2025-02-07 10:29:30,292 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-07 10:29:39,152 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-07 10:33:17,604 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-07 10:33:20,745 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-07 12:00:01,553 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-07 13:22:18,940 INFO /home/<USER>/.local/bin/bench list-apps
2025-02-07 16:00:16,042 INFO /home/<USER>/.local/bin/bench use explore
2025-02-07 16:00:30,843 INFO /home/<USER>/.local/bin/bench start
2025-02-07 16:00:31,359 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-07 16:00:31,457 INFO /home/<USER>/.local/bin/bench worker
2025-02-07 16:00:31,463 INFO /home/<USER>/.local/bin/bench watch
2025-02-07 16:00:31,504 INFO /home/<USER>/.local/bin/bench schedule
2025-02-10 08:43:15,498 INFO /home/<USER>/.local/bin/bench start
2025-02-10 08:43:16,021 INFO /home/<USER>/.local/bin/bench schedule
2025-02-10 08:43:16,022 INFO /home/<USER>/.local/bin/bench watch
2025-02-10 08:43:16,209 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-10 08:43:16,235 INFO /home/<USER>/.local/bin/bench worker
2025-02-10 08:43:48,080 INFO /home/<USER>/.local/bin/bench use explore
2025-02-10 09:04:38,615 INFO /home/<USER>/.local/bin/bench start
2025-02-10 09:04:39,209 INFO /home/<USER>/.local/bin/bench schedule
2025-02-10 09:04:39,233 INFO /home/<USER>/.local/bin/bench watch
2025-02-10 09:04:39,299 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-10 09:04:39,315 INFO /home/<USER>/.local/bin/bench worker
2025-02-10 09:05:04,521 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-10 09:05:17,393 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-10 09:51:55,655 INFO /home/<USER>/.local/bin/bench use working
2025-02-10 10:24:34,585 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-10 10:24:45,355 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-10 10:25:19,487 INFO /home/<USER>/.local/bin/bench start
2025-02-10 10:25:20,052 INFO /home/<USER>/.local/bin/bench schedule
2025-02-10 10:25:20,107 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-10 10:25:20,203 INFO /home/<USER>/.local/bin/bench worker
2025-02-10 10:25:20,220 INFO /home/<USER>/.local/bin/bench watch
2025-02-10 12:00:01,356 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-10 15:45:44,896 INFO /home/<USER>/.local/bin/bench use explore
2025-02-10 15:46:00,766 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-10 15:46:09,657 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-10 15:46:36,308 INFO /home/<USER>/.local/bin/bench start
2025-02-10 15:46:36,931 INFO /home/<USER>/.local/bin/bench watch
2025-02-10 15:46:36,953 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-10 15:46:36,988 INFO /home/<USER>/.local/bin/bench worker
2025-02-10 15:46:37,006 INFO /home/<USER>/.local/bin/bench schedule
2025-02-10 15:47:21,664 INFO /home/<USER>/.local/bin/bench use working
2025-02-10 15:47:29,285 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-10 15:47:36,648 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-10 15:47:51,181 INFO /home/<USER>/.local/bin/bench start
2025-02-10 15:47:51,729 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-10 15:47:51,733 INFO /home/<USER>/.local/bin/bench schedule
2025-02-10 15:47:51,841 INFO /home/<USER>/.local/bin/bench watch
2025-02-10 15:47:51,889 INFO /home/<USER>/.local/bin/bench worker
2025-02-10 18:00:01,951 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-11 08:41:45,716 INFO /home/<USER>/.local/bin/bench use explore
2025-02-11 08:42:00,078 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-11 08:42:10,084 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-11 08:42:21,155 INFO /home/<USER>/.local/bin/bench start
2025-02-11 08:42:21,778 INFO /home/<USER>/.local/bin/bench schedule
2025-02-11 08:42:21,851 INFO /home/<USER>/.local/bin/bench watch
2025-02-11 08:42:21,947 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-11 08:42:22,012 INFO /home/<USER>/.local/bin/bench worker
2025-02-11 12:00:01,529 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-11 18:00:01,630 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-12 08:37:56,285 INFO /home/<USER>/.local/bin/bench start
2025-02-12 08:37:56,887 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 08:37:56,935 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 08:37:57,082 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 08:37:57,123 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 10:13:58,282 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-12 10:14:08,188 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-12 10:14:26,424 INFO /home/<USER>/.local/bin/bench start
2025-02-12 10:14:27,200 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 10:14:27,344 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 10:14:27,355 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 10:14:27,401 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 11:04:17,031 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/wiki --branch master
2025-02-12 11:04:17,043 LOG Getting wiki
2025-02-12 11:04:17,044 DEBUG cd ./apps && git clone https://github.com/frappe/wiki --branch master --depth 1 --origin upstream
2025-02-12 11:04:21,137 LOG Installing wiki
2025-02-12 11:04:21,138 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wiki 
2025-02-12 11:04:25,704 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && yarn install --check-files
2025-02-12 11:04:29,775 DEBUG bench build --app wiki
2025-02-12 11:04:30,048 INFO /home/<USER>/.local/bin/bench build --app wiki
2025-02-12 11:05:28,809 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-12 11:05:29,073 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-12 11:05:29,073 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-12 11:06:28,650 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki --force
2025-02-12 11:06:58,866 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki --force
2025-02-12 11:09:34,699 INFO /home/<USER>/.local/bin/bench start
2025-02-12 11:09:35,227 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 11:09:35,389 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 11:09:35,391 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 11:09:35,410 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 11:09:44,611 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-02-12 11:10:30,798 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-12 11:10:40,470 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-12 11:10:53,444 INFO /home/<USER>/.local/bin/bench start
2025-02-12 11:10:53,953 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 11:10:54,010 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 11:10:54,088 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 11:10:54,116 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 11:14:40,686 INFO /home/<USER>/.local/bin/bench --site explore reinstall --force
2025-02-12 11:14:53,928 INFO /home/<USER>/.local/bin/bench --site explore reinstall
2025-02-12 11:26:38,912 INFO /home/<USER>/.local/bin/bench start
2025-02-12 11:26:39,515 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 11:26:39,606 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 11:26:39,610 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 11:26:39,627 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 11:29:55,037 INFO /home/<USER>/.local/bin/bench --site explore install-app csf_tz
2025-02-12 11:32:01,355 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-02-12 11:32:46,953 INFO /home/<USER>/.local/bin/bench --site explore install-app wcfcb_zm
2025-02-12 11:33:00,534 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki --force
2025-02-12 11:33:23,007 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-02-12 11:33:56,633 INFO /home/<USER>/.local/bin/bench start
2025-02-12 11:33:57,341 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 11:33:57,382 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 11:33:57,514 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 11:33:57,517 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 12:00:01,643 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-12 16:02:34,701 INFO /home/<USER>/.local/bin/bench start
2025-02-12 16:02:35,473 INFO /home/<USER>/.local/bin/bench watch
2025-02-12 16:02:35,525 INFO /home/<USER>/.local/bin/bench schedule
2025-02-12 16:02:35,617 INFO /home/<USER>/.local/bin/bench worker
2025-02-12 16:02:36,084 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-12 18:00:02,301 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-13 08:41:45,197 INFO /home/<USER>/.local/bin/bench start
2025-02-13 08:41:45,849 INFO /home/<USER>/.local/bin/bench worker
2025-02-13 08:41:45,854 INFO /home/<USER>/.local/bin/bench watch
2025-02-13 08:41:45,892 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-13 08:41:45,969 INFO /home/<USER>/.local/bin/bench schedule
2025-02-13 12:00:02,104 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-13 18:00:02,322 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-13 22:14:05,397 INFO /home/<USER>/.local/bin/bench start
2025-02-13 22:14:05,964 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-13 22:14:06,058 INFO /home/<USER>/.local/bin/bench schedule
2025-02-13 22:14:06,174 INFO /home/<USER>/.local/bin/bench worker
2025-02-13 22:14:06,295 INFO /home/<USER>/.local/bin/bench watch
2025-02-14 08:40:48,641 INFO /home/<USER>/.local/bin/bench use working
2025-02-14 08:41:00,354 INFO /home/<USER>/.local/bin/bench start
2025-02-14 08:41:00,935 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-14 08:41:01,027 INFO /home/<USER>/.local/bin/bench watch
2025-02-14 08:41:01,030 INFO /home/<USER>/.local/bin/bench worker
2025-02-14 08:41:01,036 INFO /home/<USER>/.local/bin/bench schedule
2025-02-14 10:59:59,876 INFO /home/<USER>/.local/bin/bench start
2025-02-14 11:00:00,636 INFO /home/<USER>/.local/bin/bench worker
2025-02-14 11:00:00,740 INFO /home/<USER>/.local/bin/bench watch
2025-02-14 11:00:00,840 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-14 11:00:00,919 INFO /home/<USER>/.local/bin/bench schedule
2025-02-14 12:00:02,373 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-14 12:18:50,036 INFO /home/<USER>/.local/bin/bench use explore
2025-02-14 12:19:06,412 INFO /home/<USER>/.local/bin/bench start
2025-02-14 12:19:07,085 INFO /home/<USER>/.local/bin/bench worker
2025-02-14 12:19:07,126 INFO /home/<USER>/.local/bin/bench watch
2025-02-14 12:19:07,173 INFO /home/<USER>/.local/bin/bench schedule
2025-02-14 12:19:07,203 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-14 17:44:57,392 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-02-14 17:44:57,416 LOG Getting csf_tz
2025-02-14 17:44:57,417 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-02-14 17:45:03,229 LOG Installing csf_tz
2025-02-14 17:45:03,230 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-02-14 17:45:10,407 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-02-14 17:45:21,051 DEBUG bench build --app csf_tz
2025-02-14 17:45:21,309 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-02-14 17:45:34,400 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-14 17:45:34,753 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-14 17:45:34,753 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-14 17:45:46,769 INFO /home/<USER>/.local/bin/bench migrate
2025-02-14 18:00:02,144 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-14 18:04:19,451 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-14 18:04:29,041 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-15 12:00:01,391 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-15 18:00:01,539 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-16 12:00:01,455 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-16 12:39:04,085 INFO /home/<USER>/.local/bin/bench start
2025-02-16 12:39:04,649 INFO /home/<USER>/.local/bin/bench schedule
2025-02-16 12:39:04,775 INFO /home/<USER>/.local/bin/bench watch
2025-02-16 12:39:04,799 INFO /home/<USER>/.local/bin/bench worker
2025-02-16 12:39:04,891 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-16 12:41:49,550 INFO /home/<USER>/.local/bin/bench migrate
2025-02-16 12:42:45,429 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-17 08:32:01,398 INFO /home/<USER>/.local/bin/bench start
2025-02-17 08:32:02,043 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-17 08:32:02,249 INFO /home/<USER>/.local/bin/bench schedule
2025-02-17 08:32:02,310 INFO /home/<USER>/.local/bin/bench watch
2025-02-17 08:32:02,330 INFO /home/<USER>/.local/bin/bench worker
2025-02-17 10:07:07,889 INFO /home/<USER>/.local/bin/bench use working
2025-02-17 10:07:28,049 INFO /home/<USER>/.local/bin/bench start
2025-02-17 10:07:28,657 INFO /home/<USER>/.local/bin/bench schedule
2025-02-17 10:07:28,708 INFO /home/<USER>/.local/bin/bench watch
2025-02-17 10:07:28,780 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-17 10:07:28,803 INFO /home/<USER>/.local/bin/bench worker
2025-02-17 12:00:02,326 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-17 14:24:31,234 INFO /home/<USER>/.local/bin/bench start
2025-02-17 14:24:31,851 INFO /home/<USER>/.local/bin/bench schedule
2025-02-17 14:24:31,926 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-17 14:24:31,974 INFO /home/<USER>/.local/bin/bench worker
2025-02-17 14:24:32,010 INFO /home/<USER>/.local/bin/bench watch
2025-02-17 17:28:16,259 INFO /home/<USER>/.local/bin/bench start
2025-02-17 17:28:16,897 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-17 17:28:16,924 INFO /home/<USER>/.local/bin/bench worker
2025-02-17 17:28:16,937 INFO /home/<USER>/.local/bin/bench schedule
2025-02-17 17:28:17,019 INFO /home/<USER>/.local/bin/bench watch
2025-02-17 18:00:01,779 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-18 08:41:17,376 INFO /home/<USER>/.local/bin/bench start
2025-02-18 08:41:17,981 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 08:41:18,067 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 08:41:18,096 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 08:41:18,113 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 09:40:38,085 INFO /home/<USER>/.local/bin/bench start
2025-02-18 09:40:39,394 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 09:40:40,006 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 09:40:40,028 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 09:40:40,416 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 09:54:44,834 INFO /home/<USER>/.local/bin/bench use explore
2025-02-18 09:54:57,098 INFO /home/<USER>/.local/bin/bench start
2025-02-18 09:54:57,897 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 09:54:58,213 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 09:54:58,246 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 09:54:58,482 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 10:41:04,125 INFO /home/<USER>/.local/bin/bench use working
2025-02-18 10:41:11,694 INFO /home/<USER>/.local/bin/bench start
2025-02-18 10:41:12,302 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 10:41:12,322 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 10:41:12,437 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 10:41:12,487 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 12:00:01,690 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-18 12:03:20,693 INFO /home/<USER>/.local/bin/bench use explore
2025-02-18 12:03:34,677 INFO /home/<USER>/.local/bin/bench start
2025-02-18 12:03:35,715 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 12:03:35,888 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 12:03:36,258 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 12:03:36,514 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 13:09:25,467 INFO /home/<USER>/.local/bin/bench get-app drive --branch main
2025-02-18 13:09:26,553 LOG Getting drive
2025-02-18 13:09:26,554 DEBUG cd ./apps && git clone https://github.com/frappe/drive.git --branch main --depth 1 --origin upstream
2025-02-18 13:09:43,416 LOG Installing drive
2025-02-18 13:09:43,417 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/drive 
2025-02-18 13:10:34,073 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && yarn install --check-files
2025-02-18 13:12:00,229 DEBUG bench build --app drive
2025-02-18 13:12:00,704 INFO /home/<USER>/.local/bin/bench build --app drive
2025-02-18 13:32:43,258 INFO /home/<USER>/.local/bin/bench start
2025-02-18 13:32:43,864 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 13:32:43,900 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 13:32:43,984 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 13:32:43,995 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 13:33:53,023 INFO /home/<USER>/.local/bin/bench --site install-app drive
2025-02-18 13:34:12,340 INFO /home/<USER>/.local/bin/bench --site explore install-app drive
2025-02-18 13:34:31,156 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 13:35:21,298 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-18 13:35:28,969 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-18 14:20:58,626 INFO /home/<USER>/.local/bin/bench get-app --branch develop hrms https://github.com/frappe/hrms.git
2025-02-18 14:20:58,641 LOG Getting hrms
2025-02-18 14:20:58,641 DEBUG cd ./apps && git clone https://github.com/frappe/hrms.git --branch develop --depth 1 --origin upstream
2025-02-18 14:21:05,417 LOG Installing hrms
2025-02-18 14:21:05,421 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-02-18 14:21:12,093 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-02-18 14:22:29,157 DEBUG bench build --app hrms
2025-02-18 14:22:29,550 INFO /home/<USER>/.local/bin/bench build --app hrms
2025-02-18 14:23:34,716 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-18 14:23:35,202 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-18 14:23:35,202 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-18 14:24:02,546 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms
2025-02-18 14:24:37,446 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms --force
2025-02-18 14:25:17,029 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms --force
2025-02-18 14:26:51,324 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 14:28:05,651 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-18 14:28:18,427 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-18 14:34:13,239 INFO /home/<USER>/.local/bin/bench --site explore reinstall
2025-02-18 14:40:21,289 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz --force
2025-02-18 14:40:46,135 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 14:41:30,948 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki --force
2025-02-18 14:41:43,006 INFO /home/<USER>/.local/bin/bench --site explore install-app wiki --force
2025-02-18 14:42:08,277 INFO /home/<USER>/.local/bin/bench --site explore install-app wcfcb_zm --force
2025-02-18 14:42:15,420 INFO /home/<USER>/.local/bin/bench --site explore install-app wcfcb_zm --force
2025-02-18 14:42:35,246 INFO /home/<USER>/.local/bin/bench --site explore install-app drive --force
2025-02-18 14:42:43,083 INFO /home/<USER>/.local/bin/bench --site explore install-app drive --force
2025-02-18 14:42:57,985 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 14:44:38,957 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-18 14:45:18,513 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-18 14:47:50,549 INFO /home/<USER>/.local/bin/bench start
2025-02-18 14:47:51,089 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 14:47:51,124 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 14:47:51,166 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 14:47:51,208 INFO /home/<USER>/.local/bin/bench worker
                                                                                                                                                                                                                                                                                                                                                                    2025-02-18 15:17:55,974 INFO /home/<USER>/.local/bin/bench start
2025-02-18 15:17:56,564 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 15:17:56,623 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 15:17:56,645 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 15:17:56,673 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 15:41:13,091 INFO /home/<USER>/.local/bin/bench start
2025-02-18 15:41:13,811 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 15:41:13,861 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 15:41:13,898 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 15:41:14,004 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 16:13:41,133 INFO /home/<USER>/.local/bin/bench --site explore restore /home/<USER>/Downloads/Telegram Desktop/20250121_142722-icd-dev_aakvaerp_com-database.sql
2025-02-18 16:15:05,715 INFO /home/<USER>/.local/bin/bench --site explore restore 20250121_142722-icd-dev_aakvaerp_com-database.sql
2025-02-18 16:16:40,884 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 16:20:27,215 INFO /home/<USER>/.local/bin/bench --site explore clear-site
2025-02-18 16:20:39,935 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-18 16:20:49,324 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-18 16:21:06,136 INFO /home/<USER>/.local/bin/bench start
2025-02-18 16:21:06,816 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 16:21:06,845 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 16:21:06,859 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 16:21:06,915 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 16:26:05,922 INFO /home/<USER>/.local/bin/bench --site explore set-admin-password aakvatech
2025-02-18 16:26:12,672 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 16:26:43,021 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-18 16:26:47,584 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-18 16:26:59,246 INFO /home/<USER>/.local/bin/bench start
2025-02-18 16:26:59,883 INFO /home/<USER>/.local/bin/bench worker
2025-02-18 16:26:59,885 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-18 16:26:59,887 INFO /home/<USER>/.local/bin/bench watch
2025-02-18 16:26:59,978 INFO /home/<USER>/.local/bin/bench schedule
2025-02-18 16:42:14,363 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-14
2025-02-18 16:42:14,378 LOG Getting csf_tz
2025-02-18 16:42:14,378 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-14 --depth 1 --origin upstream
2025-02-18 16:42:18,115 LOG Installing csf_tz
2025-02-18 16:42:18,115 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-02-18 16:42:23,514 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-02-18 16:42:26,151 DEBUG bench build --app csf_tz
2025-02-18 16:42:26,482 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-02-18 16:42:38,443 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-18 16:42:38,740 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-18 16:42:38,740 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-18 16:42:48,684 INFO /home/<USER>/.local/bin/bench migrate
2025-02-18 16:43:48,512 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-18 16:43:57,736 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-18 18:00:01,857 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-19 08:36:28,861 INFO /home/<USER>/.local/bin/bench start
2025-02-19 08:36:29,450 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 08:36:29,498 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 08:36:29,499 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 08:36:29,520 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 08:46:39,972 INFO /home/<USER>/.local/bin/bench use working
2025-02-19 08:47:21,819 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-19 08:47:33,776 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-19 08:48:02,909 INFO /home/<USER>/.local/bin/bench start
2025-02-19 08:48:03,730 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 08:48:04,004 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 08:48:04,039 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 08:48:04,063 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 08:49:32,467 INFO /home/<USER>/.local/bin/bench --site working install-app wiki
2025-02-19 08:49:46,100 INFO /home/<USER>/.local/bin/bench migrate
2025-02-19 10:19:51,021 INFO /home/<USER>/.local/bin/bench use explore
2025-02-19 10:20:08,258 INFO /home/<USER>/.local/bin/bench start
2025-02-19 10:20:09,052 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 10:20:09,108 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 10:20:09,141 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 10:20:09,150 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 11:14:40,593 INFO /home/<USER>/.local/bin/bench start
2025-02-19 11:14:41,147 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 11:14:41,210 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 11:14:41,342 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 11:14:41,471 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 12:00:01,529 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-19 14:38:35,310 INFO /home/<USER>/.local/bin/bench start
2025-02-19 14:38:36,161 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 14:38:36,251 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 14:38:36,270 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 14:38:36,440 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 14:41:11,284 INFO /home/<USER>/.local/bin/bench use explore
2025-02-19 14:42:41,432 INFO /home/<USER>/.local/bin/bench migrate
2025-02-19 14:45:10,892 INFO /home/<USER>/.local/bin/bench --site exploreclear-cache
2025-02-19 14:46:19,453 INFO /home/<USER>/.local/bin/bench --site exploreclear-cache
2025-02-19 14:46:34,666 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-19 14:47:36,027 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-19 14:50:53,916 INFO /home/<USER>/.local/bin/bench use working
2025-02-19 14:51:07,675 INFO /home/<USER>/.local/bin/bench migrate
2025-02-19 14:51:46,566 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-19 14:51:51,263 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-19 14:52:03,774 INFO /home/<USER>/.local/bin/bench start
2025-02-19 14:52:04,333 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 14:52:04,348 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 14:52:04,388 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 14:52:04,460 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 15:28:57,918 INFO /home/<USER>/.local/bin/bench use explore
2025-02-19 15:29:06,263 INFO /home/<USER>/.local/bin/bench migrate
2025-02-19 15:30:04,480 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-19 15:30:16,487 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-19 15:31:01,574 INFO /home/<USER>/.local/bin/bench start
2025-02-19 15:31:02,126 INFO /home/<USER>/.local/bin/bench schedule
2025-02-19 15:31:02,130 INFO /home/<USER>/.local/bin/bench worker
2025-02-19 15:31:02,232 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-19 15:31:02,295 INFO /home/<USER>/.local/bin/bench watch
2025-02-19 18:00:01,911 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-20 08:44:28,995 INFO /home/<USER>/.local/bin/bench start
2025-02-20 08:44:29,804 INFO /home/<USER>/.local/bin/bench watch
2025-02-20 08:44:29,813 INFO /home/<USER>/.local/bin/bench worker
2025-02-20 08:44:29,816 INFO /home/<USER>/.local/bin/bench schedule
2025-02-20 08:44:29,865 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-20 12:00:01,775 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-20 15:22:01,942 INFO /home/<USER>/.local/bin/bench new-site rental
2025-02-20 15:26:23,916 INFO /home/<USER>/.local/bin/bench --site rental add-to-hosts
2025-02-20 15:26:54,711 INFO /home/<USER>/.local/bin/bench --site rental install-app erpnext
2025-02-20 15:28:42,800 INFO /home/<USER>/.local/bin/bench start
2025-02-20 15:28:43,349 INFO /home/<USER>/.local/bin/bench schedule
2025-02-20 15:28:43,354 INFO /home/<USER>/.local/bin/bench watch
2025-02-20 15:28:43,532 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-20 15:28:43,594 INFO /home/<USER>/.local/bin/bench worker
2025-02-20 15:30:38,159 INFO /home/<USER>/.local/bin/bench use rental
2025-02-20 15:30:52,263 INFO /home/<USER>/.local/bin/bench start
2025-02-20 15:30:52,736 INFO /home/<USER>/.local/bin/bench watch
2025-02-20 15:30:52,851 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-20 15:30:52,898 INFO /home/<USER>/.local/bin/bench worker
2025-02-20 15:30:52,912 INFO /home/<USER>/.local/bin/bench schedule
2025-02-20 15:32:48,250 INFO /home/<USER>/.local/bin/bench new-app renting
2025-02-20 15:32:48,260 LOG creating new app renting
2025-02-20 15:34:14,496 LOG Installing renting
2025-02-20 15:34:14,499 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/renting 
2025-02-20 15:34:19,433 DEBUG bench build --app renting
2025-02-20 15:34:19,656 INFO /home/<USER>/.local/bin/bench build --app renting
2025-02-20 15:34:23,801 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-20 15:34:24,090 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-20 15:34:24,090 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-20 16:01:19,397 INFO /home/<USER>/.local/bin/bench --site rental install-app renting
2025-02-20 16:02:01,443 INFO /home/<USER>/.local/bin/bench use explore
2025-02-20 16:02:08,326 INFO /home/<USER>/.local/bin/bench migrate
2025-02-20 16:03:10,572 INFO /home/<USER>/.local/bin/bench start
2025-02-20 16:03:11,102 INFO /home/<USER>/.local/bin/bench worker
2025-02-20 16:03:11,107 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-20 16:03:11,115 INFO /home/<USER>/.local/bin/bench schedule
2025-02-20 16:03:11,417 INFO /home/<USER>/.local/bin/bench watch
2025-02-20 17:01:18,197 INFO /home/<USER>/.local/bin/bench --site your_site console
2025-02-20 17:01:37,996 INFO /home/<USER>/.local/bin/bench --site explore console
2025-02-20 17:50:24,953 INFO /home/<USER>/.local/bin/bench use rental
2025-02-20 17:50:47,293 INFO /home/<USER>/.local/bin/bench start
2025-02-20 17:50:47,828 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-20 17:50:47,920 INFO /home/<USER>/.local/bin/bench worker
2025-02-20 17:50:48,024 INFO /home/<USER>/.local/bin/bench schedule
2025-02-20 17:50:48,024 INFO /home/<USER>/.local/bin/bench watch
2025-02-20 17:51:07,833 INFO /home/<USER>/.local/bin/bench --site rental clear-cache
2025-02-20 17:51:16,499 INFO /home/<USER>/.local/bin/bench --site rental clear-website-cache
2025-02-20 18:00:01,995 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-20 18:04:59,220 INFO /home/<USER>/.local/bin/bench start
2025-02-20 18:04:59,990 INFO /home/<USER>/.local/bin/bench schedule
2025-02-20 18:05:00,096 INFO /home/<USER>/.local/bin/bench watch
2025-02-20 18:05:00,176 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-20 18:05:00,228 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 08:54:37,204 INFO /home/<USER>/.local/bin/bench start
2025-02-21 08:54:37,798 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 08:54:37,807 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 08:54:37,841 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 08:54:37,890 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 09:08:20,503 INFO /home/<USER>/.local/bin/bench start
2025-02-21 09:08:21,655 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 09:08:21,748 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 09:08:21,942 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 09:08:22,422 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 09:49:55,112 INFO /home/<USER>/.local/bin/bench use explore
2025-02-21 09:50:36,955 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-21 09:50:48,438 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-21 09:51:01,022 INFO /home/<USER>/.local/bin/bench start
2025-02-21 09:51:01,873 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 09:51:01,919 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 09:51:02,016 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 09:51:02,033 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 09:53:04,288 INFO /home/<USER>/.local/bin/bench use working
2025-02-21 09:53:25,822 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-21 09:53:45,483 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-21 09:54:09,838 INFO /home/<USER>/.local/bin/bench start
2025-02-21 09:54:10,407 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 09:54:10,506 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 09:54:10,581 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 09:54:10,818 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 10:17:04,777 INFO /home/<USER>/.local/bin/bench start
2025-02-21 10:17:05,362 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 10:17:05,498 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 10:17:05,553 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 10:17:05,780 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 12:00:01,899 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-21 12:26:42,502 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-21 12:26:52,000 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-21 12:39:11,973 INFO /home/<USER>/.local/bin/bench migrate
2025-02-21 12:40:18,156 INFO /home/<USER>/.local/bin/bench start
2025-02-21 12:40:18,831 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 12:40:18,833 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 12:40:18,873 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 12:40:18,947 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 15:30:55,299 INFO /home/<USER>/.local/bin/bench migrate
2025-02-21 17:33:56,606 INFO /home/<USER>/.local/bin/bench use working
2025-02-21 17:34:34,926 INFO /home/<USER>/.local/bin/bench migrate
2025-02-21 17:35:16,484 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-21 17:35:25,593 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-21 17:35:53,965 INFO /home/<USER>/.local/bin/bench start
2025-02-21 17:35:54,509 INFO /home/<USER>/.local/bin/bench worker
2025-02-21 17:35:54,582 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-21 17:35:54,591 INFO /home/<USER>/.local/bin/bench watch
2025-02-21 17:35:54,661 INFO /home/<USER>/.local/bin/bench schedule
2025-02-21 18:00:01,905 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-22 14:27:51,121 INFO /home/<USER>/.local/bin/bench start
2025-02-22 14:27:51,677 INFO /home/<USER>/.local/bin/bench schedule
2025-02-22 14:27:51,757 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-22 14:27:51,769 INFO /home/<USER>/.local/bin/bench worker
2025-02-22 14:27:51,781 INFO /home/<USER>/.local/bin/bench watch
2025-02-22 18:00:01,495 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-23 00:00:01,721 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-23 18:00:01,618 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-24 08:43:21,354 INFO /home/<USER>/.local/bin/bench use rental
2025-02-24 08:43:35,558 INFO /home/<USER>/.local/bin/bench --site rental clear-cache
2025-02-24 08:43:45,191 INFO /home/<USER>/.local/bin/bench --site rental clear-website-cache
2025-02-24 08:44:06,819 INFO /home/<USER>/.local/bin/bench start
2025-02-24 08:44:07,377 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 08:44:07,476 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 08:44:07,493 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 08:44:07,527 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 09:22:23,478 INFO /home/<USER>/.local/bin/bench start
2025-02-24 09:22:25,009 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 09:22:25,271 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 09:22:25,585 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 09:22:26,046 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 11:55:49,178 INFO /home/<USER>/.local/bin/bench use explore
2025-02-24 11:56:01,548 INFO /home/<USER>/.local/bin/bench start
2025-02-24 11:56:02,259 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 11:56:02,416 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 11:56:02,427 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 11:56:02,595 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 12:00:01,619 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-24 12:19:52,286 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-24 12:20:01,549 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-24 13:07:49,906 INFO /home/<USER>/.local/bin/bench use rental
2025-02-24 13:08:20,033 INFO /home/<USER>/.local/bin/bench --site rental clear-cache
2025-02-24 13:08:30,336 INFO /home/<USER>/.local/bin/bench --site rental clear-website-cache
2025-02-24 13:08:48,639 INFO /home/<USER>/.local/bin/bench start
2025-02-24 13:08:49,171 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 13:08:49,321 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 13:08:49,386 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 13:08:49,390 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 15:06:13,698 INFO /home/<USER>/.local/bin/bench use explore
2025-02-24 15:06:31,184 INFO /home/<USER>/.local/bin/bench start
2025-02-24 15:06:31,756 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 15:06:31,764 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 15:06:31,812 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 15:06:31,813 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 16:00:25,207 INFO /home/<USER>/.local/bin/bench use rental
2025-02-24 16:00:31,923 INFO /home/<USER>/.local/bin/bench --site rental clear-cache
2025-02-24 16:00:37,839 INFO /home/<USER>/.local/bin/bench --site rental clear-website-cache
2025-02-24 16:01:00,353 INFO /home/<USER>/.local/bin/bench start
2025-02-24 16:01:00,930 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 16:01:00,970 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 16:01:00,982 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 16:01:01,043 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 17:51:15,645 INFO /home/<USER>/.local/bin/bench use working
2025-02-24 17:51:27,357 INFO /home/<USER>/.local/bin/bench start
2025-02-24 17:51:27,878 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-24 17:51:27,946 INFO /home/<USER>/.local/bin/bench watch
2025-02-24 17:51:27,967 INFO /home/<USER>/.local/bin/bench schedule
2025-02-24 17:51:28,094 INFO /home/<USER>/.local/bin/bench worker
2025-02-24 18:00:02,169 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-25 08:35:50,245 INFO /home/<USER>/.local/bin/bench use rental
2025-02-25 08:36:10,254 INFO /home/<USER>/.local/bin/bench --site rental clear-cache
2025-02-25 08:36:20,144 INFO /home/<USER>/.local/bin/bench --site rental clear-website-cache
2025-02-25 08:36:29,918 INFO /home/<USER>/.local/bin/bench start
2025-02-25 08:36:30,510 INFO /home/<USER>/.local/bin/bench watch
2025-02-25 08:36:30,544 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-25 08:36:30,602 INFO /home/<USER>/.local/bin/bench schedule
2025-02-25 08:36:30,659 INFO /home/<USER>/.local/bin/bench worker
2025-02-25 12:00:01,820 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-25 18:00:02,246 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-26 08:25:56,878 INFO /home/<USER>/.local/bin/bench use working
2025-02-26 08:26:22,283 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-26 08:26:32,267 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-26 08:30:23,539 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/vfd_providers.git
2025-02-26 08:30:23,557 LOG Getting vfd_providers
2025-02-26 08:30:23,557 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/vfd_providers.git  --depth 1 --origin upstream
2025-02-26 08:30:25,369 LOG Installing vfd_providers
2025-02-26 08:30:25,369 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-02-26 08:30:27,946 DEBUG bench build --app vfd_providers
2025-02-26 08:30:28,162 INFO /home/<USER>/.local/bin/bench build --app vfd_providers
2025-02-26 08:30:41,013 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-02-26 08:30:41,267 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-02-26 08:30:41,268 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-02-26 08:31:41,357 INFO /home/<USER>/.local/bin/bench --site working install-app vfd_providers
2025-02-26 08:32:01,166 INFO /home/<USER>/.local/bin/bench start
2025-02-26 08:32:01,704 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-26 08:32:01,748 INFO /home/<USER>/.local/bin/bench worker
2025-02-26 08:32:01,852 INFO /home/<USER>/.local/bin/bench watch
2025-02-26 08:32:01,874 INFO /home/<USER>/.local/bin/bench schedule
2025-02-26 08:32:20,009 INFO /home/<USER>/.local/bin/bench migrate
2025-02-26 11:50:41,507 INFO /home/<USER>/.local/bin/bench list-fields --doctype
2025-02-26 11:55:03,524 INFO /home/<USER>/.local/bin/bench list-fields --custom
2025-02-26 11:59:49,864 INFO /home/<USER>/.local/bin/bench --site working execute frappe.db.get_values("Property Setter")
2025-02-26 12:00:01,814 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-26 12:00:53,260 INFO /home/<USER>/.local/bin/bench --site working execute frappe.db.get_values('Property Setter')
2025-02-26 12:03:20,295 INFO /home/<USER>/.local/bin/bench --site working console
2025-02-26 14:19:51,544 INFO /home/<USER>/.local/bin/bench migrate
2025-02-26 15:20:12,576 INFO /home/<USER>/.local/bin/bench migrate
2025-02-26 18:00:01,620 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-27 08:42:14,138 INFO /home/<USER>/.local/bin/bench start
2025-02-27 08:42:14,723 INFO /home/<USER>/.local/bin/bench watch
2025-02-27 08:42:14,755 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-27 08:42:14,772 INFO /home/<USER>/.local/bin/bench schedule
2025-02-27 08:42:14,832 INFO /home/<USER>/.local/bin/bench worker
2025-02-27 12:00:01,732 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-27 18:00:02,000 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-28 09:07:13,882 INFO /home/<USER>/.local/bin/bench start
2025-02-28 09:07:15,196 INFO /home/<USER>/.local/bin/bench worker
2025-02-28 09:07:15,504 INFO /home/<USER>/.local/bin/bench watch
2025-02-28 09:07:15,555 INFO /home/<USER>/.local/bin/bench schedule
2025-02-28 09:07:15,882 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-28 11:49:37,482 INFO /home/<USER>/.local/bin/bench use explore
2025-02-28 11:49:55,498 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-02-28 11:50:04,054 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-02-28 11:50:18,910 INFO /home/<USER>/.local/bin/bench migrate
2025-02-28 11:50:43,480 INFO /home/<USER>/.local/bin/bench start
2025-02-28 11:50:44,057 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-28 11:50:44,063 INFO /home/<USER>/.local/bin/bench watch
2025-02-28 11:50:44,117 INFO /home/<USER>/.local/bin/bench schedule
2025-02-28 11:50:44,150 INFO /home/<USER>/.local/bin/bench worker
2025-02-28 12:00:01,876 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-02-28 14:59:44,113 INFO /home/<USER>/.local/bin/bench use working
2025-02-28 15:00:03,997 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-02-28 15:00:13,241 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-02-28 15:00:56,994 INFO /home/<USER>/.local/bin/bench start
2025-02-28 15:00:57,585 INFO /home/<USER>/.local/bin/bench schedule
2025-02-28 15:00:57,592 INFO /home/<USER>/.local/bin/bench watch
2025-02-28 15:00:57,662 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-02-28 15:00:57,714 INFO /home/<USER>/.local/bin/bench worker
2025-02-28 18:00:01,741 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-01 12:00:02,146 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-01 18:00:01,722 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-02 14:48:55,603 INFO /home/<USER>/.local/bin/bench start
2025-03-02 14:48:56,151 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-02 14:48:56,202 INFO /home/<USER>/.local/bin/bench worker
2025-03-02 14:48:56,285 INFO /home/<USER>/.local/bin/bench schedule
2025-03-02 14:48:56,321 INFO /home/<USER>/.local/bin/bench watch
2025-03-02 21:43:48,181 INFO /home/<USER>/.local/bin/bench start
2025-03-02 21:43:48,786 INFO /home/<USER>/.local/bin/bench worker
2025-03-02 21:43:48,832 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-02 21:43:48,886 INFO /home/<USER>/.local/bin/bench watch
2025-03-02 21:43:48,931 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 08:36:51,440 INFO /home/<USER>/.local/bin/bench start
2025-03-03 08:36:52,030 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 08:36:52,052 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 08:36:52,140 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 08:36:52,143 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-03 12:00:02,055 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-03 12:00:02,875 INFO /home/<USER>/.local/bin/bench start
2025-03-03 12:00:04,501 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 12:00:04,669 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 12:00:05,007 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 12:00:05,010 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-03 14:12:29,738 INFO /home/<USER>/.local/bin/bench start
2025-03-03 14:12:30,323 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 14:12:30,350 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 14:12:30,373 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-03 14:12:30,505 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 14:46:57,722 INFO /home/<USER>/.local/bin/bench use explore
2025-03-03 14:47:11,792 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-03-03 14:47:47,662 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-03-03 14:48:02,521 INFO /home/<USER>/.local/bin/bench start
2025-03-03 14:48:03,367 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 14:48:03,694 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-03 14:48:03,705 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 14:48:03,774 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 15:31:43,811 INFO /home/<USER>/.local/bin/bench start
2025-03-03 15:31:44,373 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 15:31:44,453 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-03 15:31:44,462 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 15:31:44,466 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 17:22:30,993 INFO /home/<USER>/.local/bin/bench start
2025-03-03 17:22:31,571 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 17:22:31,676 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-03 17:22:31,734 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 17:22:31,809 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 18:00:02,227 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-04 08:41:48,546 INFO /home/<USER>/.local/bin/bench start
2025-03-04 08:41:49,146 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 08:41:49,168 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-04 08:41:49,198 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 08:41:49,234 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 10:23:14,251 INFO /home/<USER>/.local/bin/bench start
2025-03-04 10:23:14,985 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-04 10:23:14,995 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 10:23:15,113 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 10:23:15,207 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 11:24:42,000 INFO /home/<USER>/.local/bin/bench start
2025-03-04 11:24:42,735 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 11:24:42,785 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 11:24:42,941 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-04 11:24:43,081 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 11:38:25,650 INFO /home/<USER>/.local/bin/bench use rental
2025-03-04 11:38:38,386 INFO /home/<USER>/.local/bin/bench --site clear-cache
2025-03-04 11:38:49,839 INFO /home/<USER>/.local/bin/bench --site rental clear-cache
2025-03-04 11:38:58,706 INFO /home/<USER>/.local/bin/bench --site rental clear-website-cache
2025-03-04 11:39:11,072 INFO /home/<USER>/.local/bin/bench start
2025-03-04 11:39:11,602 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 11:39:11,720 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 11:39:11,726 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 11:39:11,866 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-04 11:40:43,345 INFO /home/<USER>/.local/bin/bench migrate
2025-03-04 12:00:01,751 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-04 18:00:01,912 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-05 00:00:01,805 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-05 08:38:13,097 INFO /home/<USER>/.local/bin/bench start
2025-03-05 08:38:13,732 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-05 08:38:13,942 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 08:38:14,004 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 08:38:14,113 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 12:00:01,454 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-05 17:26:47,987 INFO /home/<USER>/.local/bin/bench use explore
2025-03-05 17:26:59,363 INFO /home/<USER>/.local/bin/bench start
2025-03-05 17:26:59,978 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 17:26:59,998 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 17:27:00,029 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 17:27:00,060 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-05 17:34:08,505 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-03-05 17:34:17,167 INFO App moved from apps/csf_tz to archived/apps/csf_tz-2025-03-05
2025-03-05 17:34:17,171 LOG Getting csf_tz
2025-03-05 17:34:17,171 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-03-05 17:34:20,906 LOG Installing csf_tz
2025-03-05 17:34:20,906 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-05 17:34:26,515 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-05 17:34:29,154 DEBUG bench build --app csf_tz
2025-03-05 17:34:29,450 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-03-05 17:34:42,838 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-05 17:34:43,171 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-05 17:34:43,171 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-05 17:35:07,440 INFO /home/<USER>/.local/bin/bench --site install-app csf_tz
2025-03-05 17:35:25,722 INFO /home/<USER>/.local/bin/bench --site install-app explore csf_tz
2025-03-05 17:35:42,715 INFO /home/<USER>/.local/bin/bench --site explore install-app csf_tz
2025-03-05 17:35:54,618 INFO /home/<USER>/.local/bin/bench migrate
2025-03-05 17:36:43,961 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-03-05 18:00:01,809 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-06 08:47:26,096 INFO /home/<USER>/.local/bin/bench start
2025-03-06 08:47:26,655 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 08:47:26,724 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 08:47:26,726 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 08:47:26,806 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 12:00:01,956 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-06 13:40:09,731 INFO /home/<USER>/.local/bin/bench start
2025-03-06 13:40:10,492 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 13:40:10,497 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 13:40:10,503 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 13:40:10,513 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 13:43:36,857 INFO /home/<USER>/.local/bin/bench use working
2025-03-06 13:43:42,153 INFO /home/<USER>/.local/bin/bench use working
2025-03-06 13:44:06,643 INFO /home/<USER>/.local/bin/bench start
2025-03-06 13:44:07,204 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 13:44:07,363 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 13:44:07,414 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 13:44:07,422 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 15:49:35,986 INFO /home/<USER>/.local/bin/bench use explore
2025-03-06 15:49:47,169 INFO /home/<USER>/.local/bin/bench start
2025-03-06 15:49:48,565 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 15:49:48,641 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 15:49:48,641 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 15:49:48,649 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 16:54:43,393 INFO /home/<USER>/.local/bin/bench use working
2025-03-06 16:54:49,666 INFO /home/<USER>/.local/bin/bench start
2025-03-06 16:54:50,292 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 16:54:50,356 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 16:54:50,394 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 16:54:50,434 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 17:08:49,222 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-03-06 17:08:49,237 LOG Getting csf_tz
2025-03-06 17:08:49,238 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-03-06 17:08:53,808 LOG Installing csf_tz
2025-03-06 17:08:53,809 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-06 17:08:58,933 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-06 17:09:01,020 DEBUG bench build --app csf_tz
2025-03-06 17:09:01,326 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-03-06 17:09:13,130 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-06 17:09:13,463 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-06 17:09:13,464 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-06 17:14:00,306 INFO /home/<USER>/.local/bin/bench use explore
2025-03-06 17:14:05,908 INFO /home/<USER>/.local/bin/bench start
2025-03-06 17:14:06,439 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 17:14:06,488 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 17:14:06,509 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 17:14:06,608 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 17:20:01,107 INFO /home/<USER>/.local/bin/bench use working
2025-03-06 17:20:08,010 INFO /home/<USER>/.local/bin/bench start
2025-03-06 17:20:08,544 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 17:20:08,630 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 17:20:08,788 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 17:20:08,903 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 18:00:02,221 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-06 20:26:32,400 INFO /home/<USER>/.local/bin/bench start
2025-03-06 20:26:32,982 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-06 20:26:33,164 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 20:26:33,191 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 20:26:33,278 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 21:51:07,849 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-03-06 21:51:18,140 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-03-07 08:33:55,950 INFO /home/<USER>/.local/bin/bench start
2025-03-07 08:33:56,788 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 08:33:57,130 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 08:33:57,461 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-07 08:33:57,471 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 12:00:01,812 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-07 15:25:16,461 INFO /home/<USER>/.local/bin/bench migrate
2025-03-07 18:00:02,094 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-08 12:00:01,637 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-08 18:00:01,563 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-10 09:12:12,171 INFO /home/<USER>/.local/bin/bench start
2025-03-10 09:12:12,710 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 09:12:12,712 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-10 09:12:12,845 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 09:12:12,958 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 11:42:12,676 INFO /home/<USER>/.local/bin/bench use rental
2025-03-10 11:42:18,473 INFO /home/<USER>/.local/bin/bench start
2025-03-10 11:42:19,003 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 11:42:19,042 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-10 11:42:19,062 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 11:42:19,102 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 12:00:01,812 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-10 15:54:11,553 INFO /home/<USER>/.local/bin/bench use explore
2025-03-10 15:54:19,791 INFO /home/<USER>/.local/bin/bench start
2025-03-10 15:54:20,327 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 15:54:20,373 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 15:54:20,414 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 15:54:20,472 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-10 15:57:35,795 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git
2025-03-10 15:57:35,809 LOG Getting csf_tz
2025-03-10 15:57:35,809 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git  --depth 1 --origin upstream
2025-03-10 15:57:41,720 LOG Installing csf_tz
2025-03-10 15:57:41,720 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-10 15:57:46,831 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-10 15:57:48,894 DEBUG bench build --app csf_tz
2025-03-10 15:57:49,165 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-03-10 15:57:59,066 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-10 15:57:59,450 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-10 15:57:59,450 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-10 15:58:44,970 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-14
2025-03-10 15:58:44,982 LOG Getting csf_tz
2025-03-10 15:58:44,982 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-14 --depth 1 --origin upstream
2025-03-10 15:58:49,121 LOG Installing csf_tz
2025-03-10 15:58:49,121 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-10 15:58:53,358 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-10 15:58:55,251 DEBUG bench build --app csf_tz
2025-03-10 15:58:55,494 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-03-10 15:58:59,096 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-10 15:58:59,386 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-10 15:58:59,386 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-10 15:59:16,089 INFO /home/<USER>/.local/bin/bench migrate
2025-03-10 16:22:08,626 INFO /home/<USER>/.local/bin/bench use rental
2025-03-10 16:22:16,702 INFO /home/<USER>/.local/bin/bench start
2025-03-10 16:22:17,225 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 16:22:17,294 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-10 16:22:17,324 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 16:22:17,374 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 18:00:01,700 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-10 18:12:56,114 INFO /home/<USER>/.local/bin/bench use working
2025-03-10 18:13:09,306 INFO /home/<USER>/.local/bin/bench start
2025-03-10 18:13:09,886 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 18:13:09,906 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 18:13:10,025 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 18:13:10,119 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-10 19:10:50,810 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git
2025-03-10 19:10:50,827 LOG Getting wcfcb_zm
2025-03-10 19:10:50,827 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-03-10 19:10:53,838 LOG Installing wcfcb_zm
2025-03-10 19:10:53,839 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm 
2025-03-10 19:11:00,895 DEBUG bench build --app wcfcb_zm
2025-03-10 19:11:01,267 INFO /home/<USER>/.local/bin/bench build --app wcfcb_zm
2025-03-10 19:11:16,550 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-10 19:11:16,977 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-10 19:11:16,977 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-11 08:49:07,886 INFO /home/<USER>/.local/bin/bench use rental
2025-03-11 09:03:11,323 INFO /home/<USER>/.local/bin/bench start
2025-03-11 09:03:11,855 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-11 09:03:11,988 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 09:03:11,998 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 09:03:12,092 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 12:00:02,008 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-11 14:19:51,244 INFO /home/<USER>/.local/bin/bench set-config -g server_script_enabled 1
2025-03-11 15:04:33,938 INFO /home/<USER>/.local/bin/bench use working
2025-03-11 15:05:22,698 INFO /home/<USER>/.local/bin/bench start
2025-03-11 15:05:23,272 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 15:05:23,280 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 15:05:23,372 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 15:05:23,390 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-11 15:16:13,936 INFO /home/<USER>/.local/bin/bench use working
2025-03-11 15:16:19,878 INFO /home/<USER>/.local/bin/bench start
2025-03-11 15:16:20,460 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 15:16:20,492 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-11 15:16:20,538 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 15:16:20,551 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 09:28:48,447 INFO /home/<USER>/.local/bin/bench use rental
2025-03-12 09:28:58,902 INFO /home/<USER>/.local/bin/bench start
2025-03-12 09:28:59,226 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 09:28:59,241 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 09:28:59,244 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 09:28:59,261 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-12 10:45:07,021 INFO /home/<USER>/.local/bin/bench --site rental install-app payments
2025-03-12 10:45:19,957 INFO /home/<USER>/.local/bin/bench migrate
2025-03-12 11:36:16,570 INFO /home/<USER>/.local/bin/bench get-app payments --branch version-15
2025-03-12 11:36:17,521 LOG Getting payments
2025-03-12 11:36:17,522 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git --branch version-15 --depth 1 --origin upstream
2025-03-12 11:36:19,668 LOG Installing payments
2025-03-12 11:36:19,669 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payments 
2025-03-12 11:36:24,601 DEBUG bench build --app payments
2025-03-12 11:36:24,780 INFO /home/<USER>/.local/bin/bench build --app payments
2025-03-12 11:36:35,311 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-12 11:36:35,514 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-12 11:36:35,515 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-12 11:37:12,007 INFO /home/<USER>/.local/bin/bench --site rental install-app payments
2025-03-12 11:37:23,912 INFO /home/<USER>/.local/bin/bench migrate
2025-03-12 12:00:01,632 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-12 18:00:01,968 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-13 10:12:29,206 INFO /home/<USER>/.local/bin/bench use rental
2025-03-13 10:12:34,606 INFO /home/<USER>/.local/bin/bench start
2025-03-13 10:12:34,899 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 10:12:34,900 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 10:12:34,920 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 10:12:34,940 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-13 12:00:01,449 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-13 18:00:01,155 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-14 08:39:24,112 INFO /home/<USER>/.local/bin/bench use working
2025-03-14 08:39:29,508 INFO /home/<USER>/.local/bin/bench start
2025-03-14 08:39:29,784 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-14 08:39:29,785 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 08:39:29,789 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 08:39:29,789 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 11:52:46,674 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/clearing.git
2025-03-14 11:52:46,682 LOG Getting clearing
2025-03-14 11:52:46,682 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/clearing.git  --depth 1 --origin upstream
2025-03-14 11:52:48,834 LOG Installing clearing
2025-03-14 11:52:48,835 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/clearing 
2025-03-14 11:52:51,996 DEBUG bench build --app clearing
2025-03-14 11:52:52,141 INFO /home/<USER>/.local/bin/bench build --app clearing
2025-03-14 11:58:18,679 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-14 11:58:18,850 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-14 11:58:18,850 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-14 11:59:14,297 INFO /home/<USER>/.local/bin/bench use working
2025-03-14 12:00:01,442 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-14 12:00:01,946 INFO /home/<USER>/.local/bin/bench --site working install-app clearing
2025-03-14 12:00:18,573 INFO /home/<USER>/.local/bin/bench start
2025-03-14 12:00:18,846 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 12:00:18,859 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 12:00:18,873 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-14 12:00:18,874 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 12:00:29,638 INFO /home/<USER>/.local/bin/bench migrate
2025-03-14 12:01:24,680 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-03-14 12:01:35,473 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-03-14 16:01:02,028 INFO /home/<USER>/.local/bin/bench migrate
2025-03-14 16:01:35,986 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-03-14 16:01:45,273 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-03-14 18:00:02,162 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-15 12:00:01,468 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-15 13:15:42,561 INFO /home/<USER>/.local/bin/bench start
2025-03-15 13:15:42,855 INFO /home/<USER>/.local/bin/bench schedule
2025-03-15 13:15:42,856 INFO /home/<USER>/.local/bin/bench worker
2025-03-15 13:15:42,883 INFO /home/<USER>/.local/bin/bench watch
2025-03-15 13:15:42,924 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-16 10:02:07,941 INFO /home/<USER>/.local/bin/bench start
2025-03-16 10:02:08,255 INFO /home/<USER>/.local/bin/bench schedule
2025-03-16 10:02:08,258 INFO /home/<USER>/.local/bin/bench watch
2025-03-16 10:02:08,288 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-16 10:02:08,289 INFO /home/<USER>/.local/bin/bench worker
2025-03-16 18:00:01,956 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-17 08:58:28,550 INFO /home/<USER>/.local/bin/bench start
2025-03-17 08:58:28,827 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 08:58:28,829 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 08:58:28,858 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 08:58:28,858 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-17 12:00:01,996 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-17 18:00:01,543 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-17 20:58:21,958 INFO /home/<USER>/.local/bin/bench start
2025-03-17 20:58:22,217 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 20:58:22,225 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-17 20:58:22,227 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 20:58:22,229 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:00:01,514 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-18 08:43:32,262 INFO /home/<USER>/.local/bin/bench start
2025-03-18 08:43:32,548 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 08:43:32,549 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-18 08:43:32,583 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 08:43:32,594 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 12:00:01,440 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-18 14:57:57,261 INFO /home/<USER>/.local/bin/bench start
2025-03-18 14:57:57,530 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 14:57:57,548 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-18 14:57:57,559 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 14:57:57,564 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 18:00:01,752 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-19 08:53:06,190 INFO /home/<USER>/.local/bin/bench start
2025-03-19 08:53:06,501 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 08:53:06,515 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 08:53:06,529 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 08:53:06,563 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 11:32:01,308 INFO /home/<USER>/.local/bin/bench use explore
2025-03-19 11:32:30,793 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-03-19 11:32:52,368 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-03-19 11:32:59,972 INFO /home/<USER>/.local/bin/bench start
2025-03-19 11:33:00,244 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 11:33:00,259 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 11:33:00,265 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 11:33:00,275 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 11:40:37,397 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/icd_tz.git
2025-03-19 11:40:37,405 LOG Getting icd_tz
2025-03-19 11:40:37,405 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/icd_tz.git  --depth 1 --origin upstream
2025-03-19 11:40:39,461 LOG Installing icd_tz
2025-03-19 11:40:39,461 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/icd_tz 
2025-03-19 11:40:41,708 DEBUG bench build --app icd_tz
2025-03-19 11:40:41,851 INFO /home/<USER>/.local/bin/bench build --app icd_tz
2025-03-19 11:40:50,432 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-19 11:40:50,616 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-19 11:40:50,616 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-19 11:41:06,820 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:42:02,515 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-03-19 11:42:18,102 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:49:53,023 INFO /home/<USER>/.local/bin/bench start
2025-03-19 11:49:53,313 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 11:49:53,321 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 11:49:53,334 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 11:49:53,335 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 12:00:01,190 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-19 14:10:17,813 INFO /home/<USER>/.local/bin/bench use working
2025-03-19 14:10:25,243 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:10:25,503 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 14:10:25,521 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:10:25,525 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:10:25,541 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 16:58:06,732 INFO /home/<USER>/.local/bin/bench use explore
2025-03-19 16:58:11,731 INFO /home/<USER>/.local/bin/bench start
2025-03-19 16:58:11,998 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 16:58:12,000 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 16:58:12,015 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 16:58:12,040 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:00:01,554 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-19 18:28:46,294 INFO /home/<USER>/.local/bin/bench use working
2025-03-19 18:29:06,649 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:29:06,922 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:29:06,953 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:29:06,960 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:29:06,970 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 18:30:41,024 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/flexible-budget.git
2025-03-19 18:30:41,032 LOG Getting flexible-budget
2025-03-19 18:30:41,032 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/flexible-budget.git  --depth 1 --origin upstream
2025-03-19 18:30:43,230 LOG Installing flexible_budget
2025-03-19 18:30:43,231 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-03-19 18:30:45,374 DEBUG bench build --app flexible_budget
2025-03-19 18:30:45,513 INFO /home/<USER>/.local/bin/bench build --app flexible_budget
2025-03-19 18:30:52,648 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-19 18:30:52,847 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-19 18:30:52,847 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-19 18:31:36,178 INFO /home/<USER>/.local/bin/bench --site working install-app flexible_budget
2025-03-19 18:32:06,013 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:32:47,055 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:32:47,413 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:32:47,418 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:32:47,428 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 18:32:47,434 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:32:57,440 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:36:35,874 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:39:45,539 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:42:43,979 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 21:34:31,915 INFO /home/<USER>/.local/bin/bench start
2025-03-19 21:34:32,199 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 21:34:32,201 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 21:34:32,210 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-19 21:34:32,218 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 00:00:02,074 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-20 01:08:36,431 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 01:09:48,125 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 01:12:12,310 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 08:49:38,605 INFO /home/<USER>/.local/bin/bench start
2025-03-20 08:49:38,868 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 08:49:38,875 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 08:49:38,905 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 08:49:38,905 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 08:57:11,977 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 09:21:05,233 INFO /home/<USER>/.local/bin/bench --site explore uninstall-app vfd-provider
2025-03-20 09:21:20,318 INFO /home/<USER>/.local/bin/bench --site working uninstall-app vfd-provider
2025-03-20 09:21:42,072 INFO /home/<USER>/.local/bin/bench --site working uninstall-app vfd-providers
2025-03-20 09:21:53,731 INFO /home/<USER>/.local/bin/bench --site working uninstall-app vfd_providers
2025-03-20 09:22:16,489 INFO /home/<USER>/.local/bin/bench --site explore uninstall-app vfd_providers
2025-03-20 09:22:33,302 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 09:23:21,689 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 09:23:33,704 INFO /home/<USER>/.local/bin/bench start
2025-03-20 09:23:33,947 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 09:23:33,951 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 09:23:33,955 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 09:23:34,002 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 09:23:39,100 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 09:23:55,250 INFO /home/<USER>/.local/bin/bench start
2025-03-20 09:23:55,500 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 09:23:55,501 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 09:23:55,505 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 09:23:55,544 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 09:24:11,749 INFO /home/<USER>/.local/bin/bench start
2025-03-20 09:24:12,013 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 09:24:12,020 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 09:24:12,030 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 09:24:12,039 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 09:25:08,790 INFO /home/<USER>/.local/bin/bench start
2025-03-20 09:25:09,047 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 09:25:09,058 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 09:25:09,097 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 09:25:09,098 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 09:25:23,275 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 09:28:43,600 INFO /home/<USER>/.local/bin/bench --site working remove-from-installed-apps vfd_providers
2025-03-20 09:28:48,400 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 11:35:31,253 INFO /home/<USER>/.local/bin/bench use explore
2025-03-20 11:35:36,003 INFO /home/<USER>/.local/bin/bench start
2025-03-20 11:35:36,258 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 11:35:36,270 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 11:35:36,280 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 11:35:36,315 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 11:58:35,871 INFO /home/<USER>/.local/bin/bench use working
2025-03-20 11:58:47,669 INFO /home/<USER>/.local/bin/bench start
2025-03-20 11:58:47,963 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 11:58:47,969 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 11:58:47,969 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 11:58:47,975 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 12:00:01,452 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-20 12:26:25,506 INFO /home/<USER>/.local/bin/bench use explore
2025-03-20 12:26:34,705 INFO /home/<USER>/.local/bin/bench start
2025-03-20 12:26:34,981 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 12:26:34,990 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 12:26:34,995 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 12:26:34,996 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 15:22:27,511 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/icd_tz.git
2025-03-20 15:23:51,990 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/icd_tz.git
2025-03-20 15:23:51,997 LOG Getting icd_tz
2025-03-20 15:23:51,998 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/icd_tz.git  --depth 1 --origin upstream
2025-03-20 15:23:54,298 LOG Installing icd_tz
2025-03-20 15:23:54,298 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/icd_tz 
2025-03-20 15:23:56,762 DEBUG bench build --app icd_tz
2025-03-20 15:23:56,935 INFO /home/<USER>/.local/bin/bench build --app icd_tz
2025-03-20 15:24:04,810 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-20 15:24:05,009 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-20 15:24:05,009 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-20 15:24:30,279 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 17:11:57,687 INFO /home/<USER>/.local/bin/bench use working
2025-03-20 17:12:03,064 INFO /home/<USER>/.local/bin/bench start
2025-03-20 17:12:03,358 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 17:12:03,370 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 17:12:03,371 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 17:12:03,381 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 18:00:01,505 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-20 18:29:47,359 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-03-20 18:29:55,201 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-03-20 22:21:50,116 INFO /home/<USER>/.local/bin/bench start
2025-03-20 22:21:50,389 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-20 22:21:50,392 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 22:21:50,434 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 22:21:50,445 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 23:03:28,731 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/flexible-budget.git
2025-03-20 23:03:28,739 LOG Getting flexible-budget
2025-03-20 23:03:28,739 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/flexible-budget.git  --depth 1 --origin upstream
2025-03-20 23:03:32,995 LOG Installing flexible_budget
2025-03-20 23:03:32,995 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-03-20 23:03:36,031 DEBUG bench build --app flexible_budget
2025-03-20 23:03:36,186 INFO /home/<USER>/.local/bin/bench build --app flexible_budget
2025-03-20 23:03:45,377 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-20 23:03:45,549 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-20 23:03:45,549 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-20 23:06:04,933 INFO /home/<USER>/.local/bin/bench --site working install-app flexible_budget
2025-03-20 23:06:15,322 INFO /home/<USER>/.local/bin/bench migrate
2025-03-20 23:07:53,335 INFO /home/<USER>/.local/bin/bench migrate
2025-03-21 08:18:32,244 INFO /home/<USER>/.local/bin/bench start
2025-03-21 08:18:32,545 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-21 08:18:32,553 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 08:18:32,554 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 08:18:32,565 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 10:34:13,402 INFO /home/<USER>/.local/bin/bench use explore
2025-03-21 10:34:19,729 INFO /home/<USER>/.local/bin/bench start
2025-03-21 10:34:20,012 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-21 10:34:20,014 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 10:34:20,022 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 10:34:20,045 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 10:35:32,567 INFO /home/<USER>/.local/bin/bench use working
2025-03-21 10:35:37,280 INFO /home/<USER>/.local/bin/bench start
2025-03-21 10:35:37,584 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 10:35:37,584 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 10:35:37,610 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-21 10:35:37,623 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 12:00:01,502 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-21 13:56:53,251 INFO /home/<USER>/.local/bin/bench start
2025-03-21 13:56:53,562 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 13:56:53,564 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 13:56:53,564 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-21 13:56:53,573 INFO /home/<USER>/.local/bin/bench worker
2025-03-22 12:02:57,718 INFO /home/<USER>/.local/bin/bench start
2025-03-22 12:02:58,028 INFO /home/<USER>/.local/bin/bench watch
2025-03-22 12:02:58,034 INFO /home/<USER>/.local/bin/bench schedule
2025-03-22 12:02:58,051 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-22 12:02:58,060 INFO /home/<USER>/.local/bin/bench worker
2025-03-22 12:08:38,482 INFO /home/<USER>/.local/bin/bench migrate
2025-03-22 12:11:29,318 INFO /home/<USER>/.local/bin/bench migrate
2025-03-22 12:39:20,129 INFO /home/<USER>/.local/bin/bench migrate
2025-03-22 14:22:31,351 INFO /home/<USER>/.local/bin/bench start
2025-03-22 14:22:31,677 INFO /home/<USER>/.local/bin/bench schedule
2025-03-22 14:22:31,684 INFO /home/<USER>/.local/bin/bench worker
2025-03-22 14:22:31,704 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-22 14:22:31,722 INFO /home/<USER>/.local/bin/bench watch
2025-03-22 18:00:01,580 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-24 09:18:38,358 INFO /home/<USER>/.local/bin/bench start
2025-03-24 09:18:38,633 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-24 09:18:38,635 INFO /home/<USER>/.local/bin/bench watch
2025-03-24 09:18:38,637 INFO /home/<USER>/.local/bin/bench schedule
2025-03-24 09:18:38,644 INFO /home/<USER>/.local/bin/bench worker
2025-03-24 09:25:48,502 INFO /home/<USER>/.local/bin/bench start
2025-03-24 09:25:48,783 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-24 09:25:48,801 INFO /home/<USER>/.local/bin/bench watch
2025-03-24 09:25:48,814 INFO /home/<USER>/.local/bin/bench schedule
2025-03-24 09:25:48,819 INFO /home/<USER>/.local/bin/bench worker
2025-03-24 11:18:59,598 INFO /home/<USER>/.local/bin/bench migrate
2025-03-24 12:00:01,368 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-25 10:06:33,546 INFO /home/<USER>/.local/bin/bench start
2025-03-25 10:06:33,872 INFO /home/<USER>/.local/bin/bench worker
2025-03-25 10:06:33,878 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-25 10:06:33,884 INFO /home/<USER>/.local/bin/bench schedule
2025-03-25 10:06:33,892 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 12:00:02,001 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-25 18:00:01,824 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-26 09:19:21,702 INFO /home/<USER>/.local/bin/bench start
2025-03-26 09:19:21,969 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-26 09:19:21,973 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 09:19:21,981 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 09:19:21,986 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 09:36:25,836 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 12:00:01,527 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-26 18:00:01,210 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-27 08:58:15,805 INFO /home/<USER>/.local/bin/bench start
2025-03-27 08:58:16,132 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 08:58:16,145 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-27 08:58:16,146 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 08:58:16,189 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:00:02,105 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-27 17:52:28,269 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 18:00:02,148 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-27 18:13:30,098 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 08:39:05,905 INFO /home/<USER>/.local/bin/bench start
2025-03-28 08:39:06,191 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 08:39:06,196 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 08:39:06,201 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 08:39:06,218 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-28 09:36:38,540 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-03-28 09:36:49,197 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-03-28 09:36:56,982 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 09:44:38,233 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 09:49:12,936 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 09:51:25,729 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 10:02:22,290 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 10:03:30,986 INFO /home/<USER>/.local/bin/bench start
2025-03-28 10:03:31,243 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 10:03:31,246 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 10:03:31,247 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 10:03:31,283 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-28 12:00:01,700 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-28 18:00:01,539 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-29 13:23:42,093 INFO /home/<USER>/.local/bin/bench start
2025-03-29 13:23:42,436 INFO /home/<USER>/.local/bin/bench watch
2025-03-29 13:23:42,449 INFO /home/<USER>/.local/bin/bench schedule
2025-03-29 13:23:42,453 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-29 13:23:42,465 INFO /home/<USER>/.local/bin/bench worker
2025-03-29 13:23:53,374 INFO /home/<USER>/.local/bin/bench migrate
2025-03-29 13:31:37,765 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/flexible-budget.git
2025-03-29 13:31:37,773 LOG Getting flexible-budget
2025-03-29 13:31:37,773 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/flexible-budget.git  --depth 1 --origin upstream
2025-03-29 13:31:40,234 LOG Installing flexible_budget
2025-03-29 13:31:40,235 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-03-29 13:31:44,186 DEBUG bench build --app flexible_budget
2025-03-29 13:31:44,336 INFO /home/<USER>/.local/bin/bench build --app flexible_budget
2025-03-29 13:31:56,526 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-29 13:31:56,705 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-29 13:31:56,705 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-29 13:33:28,751 INFO /home/<USER>/.local/bin/bench --site working install-app flexible_budget
2025-03-29 13:33:56,419 INFO /home/<USER>/.local/bin/bench migrate
2025-03-29 18:00:01,999 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-29 19:23:25,571 INFO /home/<USER>/.local/bin/bench migrate
2025-03-30 10:35:16,598 INFO /home/<USER>/.local/bin/bench start
2025-03-30 10:35:16,928 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-30 10:35:16,928 INFO /home/<USER>/.local/bin/bench watch
2025-03-30 10:35:16,934 INFO /home/<USER>/.local/bin/bench schedule
2025-03-30 10:35:16,941 INFO /home/<USER>/.local/bin/bench worker
2025-03-30 12:00:01,669 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-30 18:00:01,182 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-31 11:21:58,642 INFO /home/<USER>/.local/bin/bench start
2025-03-31 11:21:58,956 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 11:21:58,963 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 11:21:58,964 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-03-31 11:21:58,966 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 11:22:08,120 INFO /home/<USER>/.local/bin/bench migrate
2025-03-31 12:00:01,319 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-03-31 14:00:31,398 INFO /home/<USER>/.local/bin/bench migrate
2025-03-31 14:21:35,598 INFO /home/<USER>/.local/bin/bench get-app education --branch version-15
2025-03-31 14:21:37,749 LOG Getting education
2025-03-31 14:21:37,750 DEBUG cd ./apps && git clone https://github.com/frappe/education.git --branch version-15 --depth 1 --origin upstream
2025-03-31 14:21:58,314 LOG Installing education
2025-03-31 14:21:58,314 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/education 
2025-03-31 14:22:00,265 DEBUG bench build --app education
2025-03-31 14:22:00,417 INFO /home/<USER>/.local/bin/bench build --app education
2025-03-31 14:22:08,374 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-03-31 14:22:08,552 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-03-31 14:22:08,552 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-03-31 14:23:50,846 INFO /home/<USER>/.local/bin/bench new-site education.test
2025-03-31 14:24:56,390 INFO /home/<USER>/.local/bin/bench --site education.test add-to-hosts
2025-03-31 14:25:13,304 INFO /home/<USER>/.local/bin/bench --site working install-app education
2025-03-31 14:25:34,503 INFO /home/<USER>/.local/bin/bench --site working install-app education --force
2025-03-31 14:26:24,232 INFO /home/<USER>/.local/bin/bench migrate
2025-03-31 18:00:02,130 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-01 00:00:01,874 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-01 09:45:03,122 INFO /home/<USER>/.local/bin/bench start
2025-04-01 09:45:03,488 INFO /home/<USER>/.local/bin/bench worker
2025-04-01 09:45:03,499 INFO /home/<USER>/.local/bin/bench schedule
2025-04-01 09:45:03,500 INFO /home/<USER>/.local/bin/bench watch
2025-04-01 09:45:03,509 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-01 09:58:16,205 INFO /home/<USER>/.local/bin/bench migrate
2025-04-01 12:00:01,457 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-01 12:44:07,313 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/lms.git
2025-04-01 12:44:07,321 LOG Getting lms
2025-04-01 12:44:07,321 DEBUG cd ./apps && git clone https://github.com/frappe/lms.git  --depth 1 --origin upstream
2025-04-01 12:46:40,062 LOG Installing lms
2025-04-01 12:46:40,063 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lms 
2025-04-01 12:47:04,081 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && yarn install --check-files
2025-04-01 12:47:21,736 WARNING cd /home/<USER>/Desktop/frappe-bench/apps/lms && yarn install --check-files executed with exit code 1
2025-04-01 12:47:21,737 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/frappe/lms.git executed with exit code 1
2025-04-01 12:49:02,513 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/lms.git
2025-04-01 12:49:07,268 INFO App moved from apps/lms to archived/apps/lms-2025-04-01
2025-04-01 12:49:07,274 LOG Getting lms
2025-04-01 12:49:07,274 DEBUG cd ./apps && git clone https://github.com/frappe/lms.git  --depth 1 --origin upstream
2025-04-01 12:50:07,002 LOG Installing lms
2025-04-01 12:50:07,002 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lms 
2025-04-01 12:50:10,178 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && yarn install --check-files
2025-04-01 13:02:59,315 DEBUG bench build --app lms
2025-04-01 13:02:59,524 INFO /home/<USER>/.local/bin/bench build --app lms
2025-04-01 13:23:17,050 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-01 13:23:17,229 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-01 13:23:17,229 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-01 13:23:31,407 INFO /home/<USER>/.local/bin/bench start
2025-04-01 13:23:31,738 INFO /home/<USER>/.local/bin/bench worker
2025-04-01 13:23:31,749 INFO /home/<USER>/.local/bin/bench schedule
2025-04-01 13:23:31,750 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-01 13:23:31,758 INFO /home/<USER>/.local/bin/bench watch
2025-04-01 13:25:44,272 INFO /home/<USER>/.local/bin/bench --site working install-app lms
2025-04-01 13:26:09,683 INFO /home/<USER>/.local/bin/bench migrate
2025-04-01 14:54:12,510 INFO /home/<USER>/.local/bin/bench migrate
2025-04-01 14:54:34,596 INFO /home/<USER>/.local/bin/bench migrate
2025-04-01 16:14:46,253 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/clearing.git
2025-04-01 16:14:46,261 LOG Getting clearing
2025-04-01 16:14:46,261 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/clearing.git  --depth 1 --origin upstream
2025-04-01 16:14:52,903 LOG Installing clearing
2025-04-01 16:14:52,903 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/clearing 
2025-04-01 16:14:57,101 DEBUG bench build --app clearing
2025-04-01 16:14:57,258 INFO /home/<USER>/.local/bin/bench build --app clearing
2025-04-01 16:15:07,520 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-01 16:15:07,701 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-01 16:15:07,701 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-01 16:15:51,059 INFO /home/<USER>/.local/bin/bench --site install-app clearing
2025-04-01 16:16:03,928 INFO /home/<USER>/.local/bin/bench --site working install-app clearing
2025-04-01 16:16:12,927 INFO /home/<USER>/.local/bin/bench migrate
2025-04-01 18:00:01,852 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-01 20:45:37,461 INFO /home/<USER>/.local/bin/bench start
2025-04-01 20:45:37,767 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-01 20:45:37,772 INFO /home/<USER>/.local/bin/bench worker
2025-04-01 20:45:37,776 INFO /home/<USER>/.local/bin/bench schedule
2025-04-01 20:45:37,791 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 09:01:21,884 INFO /home/<USER>/.local/bin/bench start
2025-04-02 09:01:22,192 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 09:01:22,194 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-02 09:01:22,207 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 09:01:22,219 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 12:00:01,628 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-02 15:41:46,657 INFO /home/<USER>/.local/bin/bench migrate
2025-04-02 18:00:01,284 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-03 08:40:42,238 INFO /home/<USER>/.local/bin/bench start
2025-04-03 08:40:42,516 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-03 08:40:42,519 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 08:40:42,540 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 08:40:42,555 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 11:22:26,404 INFO /home/<USER>/.local/bin/bench migrate
2025-04-03 12:00:01,864 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-03 18:00:01,451 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-04 08:50:27,061 INFO /home/<USER>/.local/bin/bench start
2025-04-04 08:50:27,367 INFO /home/<USER>/.local/bin/bench schedule
2025-04-04 08:50:27,369 INFO /home/<USER>/.local/bin/bench watch
2025-04-04 08:50:27,372 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-04 08:50:27,375 INFO /home/<USER>/.local/bin/bench worker
2025-04-04 12:00:02,190 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-04 13:45:06,872 INFO /home/<USER>/.local/bin/bench start
2025-04-04 13:45:07,190 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-04 13:45:07,195 INFO /home/<USER>/.local/bin/bench watch
2025-04-04 13:45:07,206 INFO /home/<USER>/.local/bin/bench schedule
2025-04-04 13:45:07,221 INFO /home/<USER>/.local/bin/bench worker
2025-04-04 18:00:02,177 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-05 11:46:32,567 INFO /home/<USER>/.local/bin/bench start
2025-04-05 11:46:32,889 INFO /home/<USER>/.local/bin/bench schedule
2025-04-05 11:46:32,906 INFO /home/<USER>/.local/bin/bench watch
2025-04-05 11:46:32,910 INFO /home/<USER>/.local/bin/bench worker
2025-04-05 11:46:32,924 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-05 12:00:01,747 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-05 14:54:04,299 INFO /home/<USER>/.local/bin/bench start
2025-04-05 14:54:04,581 INFO /home/<USER>/.local/bin/bench worker
2025-04-05 14:54:04,590 INFO /home/<USER>/.local/bin/bench schedule
2025-04-05 14:54:04,614 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-05 14:54:04,627 INFO /home/<USER>/.local/bin/bench watch
2025-04-05 15:06:04,395 INFO /home/<USER>/.local/bin/bench use explore
2025-04-05 15:06:10,353 INFO /home/<USER>/.local/bin/bench start
2025-04-05 15:06:10,613 INFO /home/<USER>/.local/bin/bench schedule
2025-04-05 15:06:10,620 INFO /home/<USER>/.local/bin/bench watch
2025-04-05 15:06:10,627 INFO /home/<USER>/.local/bin/bench worker
2025-04-05 15:06:10,628 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-05 16:19:02,989 INFO /home/<USER>/.local/bin/bench use working
2025-04-05 16:19:07,814 INFO /home/<USER>/.local/bin/bench start
2025-04-05 16:19:08,101 INFO /home/<USER>/.local/bin/bench watch
2025-04-05 16:19:08,108 INFO /home/<USER>/.local/bin/bench worker
2025-04-05 16:19:08,127 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-05 16:19:08,134 INFO /home/<USER>/.local/bin/bench schedule
2025-04-05 18:00:01,774 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-06 12:00:01,355 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-06 12:51:06,071 INFO /home/<USER>/.local/bin/bench start
2025-04-06 12:51:06,375 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-06 12:51:06,378 INFO /home/<USER>/.local/bin/bench schedule
2025-04-06 12:51:06,388 INFO /home/<USER>/.local/bin/bench watch
2025-04-06 12:51:06,402 INFO /home/<USER>/.local/bin/bench worker
2025-04-06 12:51:18,704 INFO /home/<USER>/.local/bin/bench use working
2025-04-06 12:51:23,415 INFO /home/<USER>/.local/bin/bench start
2025-04-06 12:51:23,678 INFO /home/<USER>/.local/bin/bench worker
2025-04-06 12:51:23,707 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-06 12:51:23,708 INFO /home/<USER>/.local/bin/bench schedule
2025-04-06 12:51:23,717 INFO /home/<USER>/.local/bin/bench watch
2025-04-06 14:29:23,808 INFO /home/<USER>/.local/bin/bench migrate
2025-04-06 18:00:01,493 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-06 22:58:20,700 INFO /home/<USER>/.local/bin/bench migrate
2025-04-07 10:12:00,132 INFO /home/<USER>/.local/bin/bench start
2025-04-07 10:12:00,454 INFO /home/<USER>/.local/bin/bench worker
2025-04-07 10:12:00,456 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-07 10:12:00,460 INFO /home/<USER>/.local/bin/bench schedule
2025-04-07 10:12:00,477 INFO /home/<USER>/.local/bin/bench watch
2025-04-07 12:00:01,886 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-07 21:40:53,078 INFO /home/<USER>/.local/bin/bench start
2025-04-07 21:40:53,509 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-07 21:40:53,517 INFO /home/<USER>/.local/bin/bench watch
2025-04-07 21:40:53,556 INFO /home/<USER>/.local/bin/bench schedule
2025-04-07 21:40:53,560 INFO /home/<USER>/.local/bin/bench worker
2025-04-07 23:27:14,300 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 00:00:02,129 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-08 08:37:31,722 INFO /home/<USER>/.local/bin/bench start
2025-04-08 08:37:32,027 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 08:37:32,029 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 08:37:32,046 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-08 08:37:32,047 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 12:00:01,488 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-08 14:53:43,257 INFO /home/<USER>/.local/bin/bench start
2025-04-08 14:53:43,563 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 14:53:43,578 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 14:53:43,583 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 14:53:43,583 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-08 15:54:54,525 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 15:57:11,523 INFO /home/<USER>/.local/bin/bench use explore
2025-04-08 15:57:18,222 INFO /home/<USER>/.local/bin/bench start
2025-04-08 15:57:18,485 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 15:57:18,507 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 15:57:18,523 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-08 15:57:18,529 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 16:24:11,103 INFO /home/<USER>/.local/bin/bench use working
2025-04-08 16:24:19,323 INFO /home/<USER>/.local/bin/bench start
2025-04-08 16:24:19,602 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 16:24:19,615 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 16:24:19,616 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-08 16:24:19,622 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 16:24:40,910 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 16:25:48,258 INFO /home/<USER>/.local/bin/bench start
2025-04-08 16:25:53,310 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 16:29:14,706 INFO /home/<USER>/.local/bin/bench use explore
2025-04-08 16:29:19,651 INFO /home/<USER>/.local/bin/bench start
2025-04-08 16:29:19,917 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 16:29:19,917 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 16:29:19,955 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 16:29:19,969 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-08 18:00:01,471 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-09 09:37:33,629 INFO /home/<USER>/.local/bin/bench start
2025-04-09 09:37:33,948 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 09:37:33,951 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 09:37:33,955 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 09:37:33,956 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-09 09:40:31,830 INFO /home/<USER>/.local/bin/bench use working
2025-04-09 09:40:38,269 INFO /home/<USER>/.local/bin/bench start
2025-04-09 09:40:38,556 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 09:40:38,558 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-09 09:40:38,560 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 09:40:38,565 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 10:29:44,187 INFO /home/<USER>/.local/bin/bench --site working install-app wcfcb_zm
2025-04-09 10:29:55,500 INFO /home/<USER>/.local/bin/bench migrate
2025-04-09 10:40:18,536 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/wcfcb_zm.git
2025-04-09 10:40:18,544 LOG Getting wcfcb_zm
2025-04-09 10:40:18,544 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/wcfcb_zm.git  --depth 1 --origin upstream
2025-04-09 10:40:21,300 LOG Installing wcfcb_zm
2025-04-09 10:40:21,300 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm 
2025-04-09 10:40:25,540 DEBUG bench build --app wcfcb_zm
2025-04-09 10:40:25,701 INFO /home/<USER>/.local/bin/bench build --app wcfcb_zm
2025-04-09 10:41:02,802 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-09 10:41:02,987 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 10:41:02,987 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 10:41:17,850 INFO /home/<USER>/.local/bin/bench --site working install-app wcfcb_zm
2025-04-09 10:41:51,977 INFO /home/<USER>/.local/bin/bench migrate
2025-04-09 12:00:01,664 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-09 13:01:53,854 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-04-09 13:01:53,866 LOG Getting csf_tz
2025-04-09 13:01:53,866 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-04-09 13:01:58,679 LOG Installing csf_tz
2025-04-09 13:01:58,679 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-04-09 13:02:02,536 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-04-09 13:02:03,374 DEBUG bench build --app csf_tz
2025-04-09 13:02:03,518 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-04-09 13:02:11,525 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-09 13:02:11,707 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 13:02:11,707 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 13:04:27,510 INFO /home/<USER>/.local/bin/bench --site working install-app csf_tz
2025-04-09 13:06:05,531 INFO /home/<USER>/.local/bin/bench get-app erpnext --branch version-15
2025-04-09 13:06:06,438 LOG Getting erpnext
2025-04-09 13:06:06,438 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-04-09 13:06:20,979 LOG Installing erpnext
2025-04-09 13:06:20,980 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext 
2025-04-09 13:06:24,337 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && yarn install --check-files
2025-04-09 13:06:24,743 DEBUG bench build --app erpnext
2025-04-09 13:06:24,911 INFO /home/<USER>/.local/bin/bench build --app erpnext
2025-04-09 13:06:28,129 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-09 13:06:28,328 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 13:06:28,328 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 13:48:22,058 INFO /home/<USER>/.local/bin/bench --site working install-app erpnext
2025-04-09 13:48:31,748 INFO /home/<USER>/.local/bin/bench migrate
2025-04-09 18:00:01,538 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-09 21:12:35,691 INFO /home/<USER>/.local/bin/bench start
2025-04-09 21:12:35,995 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 21:12:35,997 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-09 21:12:36,015 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 21:12:36,021 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 00:00:01,547 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-10 08:41:51,264 INFO /home/<USER>/.local/bin/bench start
2025-04-10 08:41:51,620 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 08:41:51,640 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-10 08:41:51,642 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 08:41:51,670 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 09:41:46,633 INFO /home/<USER>/.local/bin/bench use explore
2025-04-10 09:41:53,235 INFO /home/<USER>/.local/bin/bench start
2025-04-10 09:41:53,555 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 09:41:53,560 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 09:41:53,588 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-10 09:41:53,597 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 10:20:22,505 INFO /home/<USER>/.local/bin/bench use working
2025-04-10 10:20:27,871 INFO /home/<USER>/.local/bin/bench start
2025-04-10 10:20:28,139 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-10 10:20:28,140 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 10:20:28,157 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 10:20:28,157 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 12:00:01,391 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-10 13:22:20,430 INFO /home/<USER>/.local/bin/bench migrate
2025-04-10 16:07:49,063 INFO /home/<USER>/.local/bin/bench use explore
2025-04-10 16:07:56,213 INFO /home/<USER>/.local/bin/bench start
2025-04-10 16:07:56,493 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-10 16:07:56,493 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 16:07:56,526 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 16:07:56,529 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 16:20:38,930 INFO /home/<USER>/.local/bin/bench use working
2025-04-10 16:20:45,130 INFO /home/<USER>/.local/bin/bench start
2025-04-10 16:20:45,388 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-10 16:20:45,415 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 16:20:45,427 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 16:20:45,429 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 18:00:01,710 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-10 21:03:55,748 INFO /home/<USER>/.local/bin/bench start
2025-04-10 21:03:56,067 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 21:03:56,081 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-10 21:03:56,086 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 21:03:56,093 INFO /home/<USER>/.local/bin/bench schedule
2025-04-11 08:35:41,764 INFO /home/<USER>/.local/bin/bench start
2025-04-11 08:35:42,073 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-11 08:35:42,082 INFO /home/<USER>/.local/bin/bench watch
2025-04-11 08:35:42,123 INFO /home/<USER>/.local/bin/bench worker
2025-04-11 08:35:42,127 INFO /home/<USER>/.local/bin/bench schedule
2025-04-11 12:00:01,379 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-11 18:00:01,794 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-12 12:00:01,780 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-12 16:18:49,363 INFO /home/<USER>/.local/bin/bench start
2025-04-12 16:18:49,691 INFO /home/<USER>/.local/bin/bench watch
2025-04-12 16:18:49,705 INFO /home/<USER>/.local/bin/bench worker
2025-04-12 16:18:49,709 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-12 16:18:49,733 INFO /home/<USER>/.local/bin/bench schedule
2025-04-12 16:19:37,178 INFO /home/<USER>/.local/bin/bench migrate
2025-04-12 18:00:01,788 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-12 21:46:40,226 INFO /home/<USER>/.local/bin/bench --site working stop
2025-04-12 21:47:00,274 INFO /home/<USER>/.local/bin/bench frappe --help
2025-04-12 21:47:20,378 INFO /home/<USER>/.local/bin/bench --help
2025-04-12 21:54:45,185 INFO /home/<USER>/.local/bin/bench migrate
2025-04-12 21:55:11,178 INFO /home/<USER>/.local/bin/bench stop
2025-04-12 21:55:35,030 INFO /home/<USER>/.local/bin/bench migrate
2025-04-13 14:24:19,707 INFO /home/<USER>/.local/bin/bench start
2025-04-13 14:24:19,999 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-13 14:24:20,005 INFO /home/<USER>/.local/bin/bench watch
2025-04-13 14:24:20,022 INFO /home/<USER>/.local/bin/bench schedule
2025-04-13 14:24:20,035 INFO /home/<USER>/.local/bin/bench worker
2025-04-13 14:25:13,679 INFO /home/<USER>/.local/bin/bench migrate
2025-04-14 12:00:01,466 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-14 12:48:54,094 INFO /home/<USER>/.local/bin/bench start
2025-04-14 12:48:54,563 INFO /home/<USER>/.local/bin/bench watch
2025-04-14 12:48:54,573 INFO /home/<USER>/.local/bin/bench schedule
2025-04-14 12:48:54,580 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-14 12:48:54,595 INFO /home/<USER>/.local/bin/bench worker
2025-04-14 12:49:04,225 INFO /home/<USER>/.local/bin/bench migrate
2025-04-14 16:28:16,794 INFO /home/<USER>/.local/bin/bench start
2025-04-14 16:28:17,139 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-14 16:28:17,142 INFO /home/<USER>/.local/bin/bench watch
2025-04-14 16:28:17,171 INFO /home/<USER>/.local/bin/bench worker
2025-04-14 16:28:17,184 INFO /home/<USER>/.local/bin/bench schedule
2025-04-14 18:00:02,122 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 00:00:01,974 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 12:00:01,505 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 12:01:17,685 INFO /home/<USER>/.local/bin/bench start
2025-04-15 12:01:18,011 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 12:01:18,015 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 12:01:18,023 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 12:01:18,027 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-15 18:00:01,365 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 20:21:43,895 INFO /home/<USER>/.local/bin/bench start
2025-04-15 20:21:44,193 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 20:21:44,203 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 20:21:44,222 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-15 20:21:44,228 INFO /home/<USER>/.local/bin/bench worker
2025-04-16 08:33:25,702 INFO /home/<USER>/.local/bin/bench start
2025-04-16 08:33:25,987 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-16 08:33:25,997 INFO /home/<USER>/.local/bin/bench watch
2025-04-16 08:33:25,998 INFO /home/<USER>/.local/bin/bench schedule
2025-04-16 08:33:26,000 INFO /home/<USER>/.local/bin/bench worker
2025-04-16 08:58:43,430 INFO /home/<USER>/.local/bin/bench migrate
2025-04-16 09:16:37,873 INFO /home/<USER>/.local/bin/bench migrate
2025-04-16 12:00:01,465 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-16 12:53:26,090 INFO /home/<USER>/.local/bin/bench migrate
2025-04-16 18:00:01,951 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-17 09:52:42,648 INFO /home/<USER>/.local/bin/bench start
2025-04-17 09:52:42,963 INFO /home/<USER>/.local/bin/bench worker
2025-04-17 09:52:42,971 INFO /home/<USER>/.local/bin/bench schedule
2025-04-17 09:52:42,983 INFO /home/<USER>/.local/bin/bench watch
2025-04-17 09:52:42,991 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-17 12:00:01,737 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-17 18:00:01,490 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-18 09:38:03,908 INFO /home/<USER>/.local/bin/bench start
2025-04-18 09:38:04,240 INFO /home/<USER>/.local/bin/bench worker
2025-04-18 09:38:04,245 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-18 09:38:04,254 INFO /home/<USER>/.local/bin/bench watch
2025-04-18 09:38:04,272 INFO /home/<USER>/.local/bin/bench schedule
2025-04-18 12:00:01,449 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-18 14:20:08,092 INFO /home/<USER>/.local/bin/bench migrate
2025-04-18 14:46:21,261 INFO /home/<USER>/.local/bin/bench --site working console
2025-04-18 15:03:57,041 INFO /home/<USER>/.local/bin/bench use education.test
2025-04-18 15:04:04,487 INFO /home/<USER>/.local/bin/bench start
2025-04-18 15:04:04,805 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-18 15:04:04,809 INFO /home/<USER>/.local/bin/bench watch
2025-04-18 15:04:04,820 INFO /home/<USER>/.local/bin/bench worker
2025-04-18 15:04:04,824 INFO /home/<USER>/.local/bin/bench schedule
2025-04-18 15:06:19,766 INFO /home/<USER>/.local/bin/bench use rental
2025-04-18 15:06:26,646 INFO /home/<USER>/.local/bin/bench start
2025-04-18 15:06:26,911 INFO /home/<USER>/.local/bin/bench schedule
2025-04-18 15:06:26,919 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-18 15:06:26,946 INFO /home/<USER>/.local/bin/bench watch
2025-04-18 15:06:26,950 INFO /home/<USER>/.local/bin/bench worker
2025-04-18 15:08:21,604 INFO /home/<USER>/.local/bin/bench --site rental install-app csf_tz
2025-04-18 15:16:39,898 INFO /home/<USER>/.local/bin/bench --site rental install-app csf_tz
2025-04-18 15:16:52,783 INFO /home/<USER>/.local/bin/bench --site rental install-app csf_tz --force
2025-04-18 15:18:22,621 INFO /home/<USER>/.local/bin/bench migrate
2025-04-18 15:27:54,513 INFO /home/<USER>/.local/bin/bench --site rental console
2025-04-18 18:00:01,958 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-19 10:21:00,241 INFO /home/<USER>/.local/bin/bench use rental
2025-04-19 10:21:05,511 INFO /home/<USER>/.local/bin/bench start
2025-04-19 10:21:05,785 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 10:21:05,795 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 10:21:05,813 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 10:21:05,828 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-19 11:13:07,151 INFO /home/<USER>/.local/bin/bench use explore
2025-04-19 11:13:15,534 INFO /home/<USER>/.local/bin/bench start
2025-04-19 11:13:15,802 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 11:13:15,809 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 11:13:15,829 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-19 11:13:15,838 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 11:22:10,926 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-04-19 11:22:10,934 LOG Getting csf_tz
2025-04-19 11:22:10,934 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-04-19 11:22:22,343 LOG Installing csf_tz
2025-04-19 11:22:22,343 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-04-19 11:22:26,296 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-04-19 11:22:32,407 DEBUG bench build --app csf_tz
2025-04-19 11:22:32,571 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-04-19 11:22:40,662 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-19 11:22:40,851 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-19 11:22:40,852 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-19 11:22:42,129 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-19 11:24:26,506 INFO /home/<USER>/.local/bin/bench --site explore install-app csf_tz
2025-04-19 11:24:48,962 INFO /home/<USER>/.local/bin/bench --site explore install-app csf_tz --force
2025-04-19 11:30:42,063 INFO /home/<USER>/.local/bin/bench migrate
2025-04-19 11:56:54,273 INFO /home/<USER>/.local/bin/bench use rental
2025-04-19 11:56:58,920 INFO /home/<USER>/.local/bin/bench start
2025-04-19 11:56:59,184 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 11:56:59,189 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-19 11:56:59,214 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 11:56:59,224 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 11:57:04,293 INFO /home/<USER>/.local/bin/bench --site rental install-app csf_tz --force
2025-04-19 12:00:01,563 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-19 12:00:30,485 INFO /home/<USER>/.local/bin/bench --site rental install-app csf_tz --force
2025-04-19 12:02:59,521 INFO /home/<USER>/.local/bin/bench use explore
2025-04-19 12:03:04,459 INFO /home/<USER>/.local/bin/bench start
2025-04-19 12:03:04,722 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 12:03:04,761 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 12:03:04,762 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 12:03:04,762 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-19 12:05:08,703 INFO /home/<USER>/.local/bin/bench migrate
2025-04-19 12:06:17,268 INFO /home/<USER>/.local/bin/bench use rental
2025-04-19 12:06:21,779 INFO /home/<USER>/.local/bin/bench start
2025-04-19 12:06:22,044 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 12:06:22,066 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 12:06:22,078 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-19 12:06:22,086 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 12:06:28,754 INFO /home/<USER>/.local/bin/bench --site rental install-app csf_tz --force
2025-04-19 12:34:28,198 INFO /home/<USER>/.local/bin/bench use working
2025-04-19 12:35:08,443 INFO /home/<USER>/.local/bin/bench start
2025-04-19 12:35:08,705 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 12:35:08,745 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-19 12:35:08,747 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 12:35:08,748 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 18:00:01,445 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-19 18:54:23,557 INFO /home/<USER>/.local/bin/bench migrate
2025-04-19 18:56:33,269 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/flexible-budget.git
2025-04-19 18:56:33,277 LOG Getting flexible-budget
2025-04-19 18:56:33,277 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/flexible-budget.git  --depth 1 --origin upstream
2025-04-19 18:56:35,889 LOG Installing flexible_budget
2025-04-19 18:56:35,890 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-04-19 18:56:38,827 DEBUG bench build --app flexible_budget
2025-04-19 18:56:38,982 INFO /home/<USER>/.local/bin/bench build --app flexible_budget
2025-04-19 18:56:46,134 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-19 18:56:46,307 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-19 18:56:46,307 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-19 18:56:47,578 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-19 18:57:24,910 INFO /home/<USER>/.local/bin/bench --site working install-app flexible_budget
2025-04-19 18:57:32,648 INFO /home/<USER>/.local/bin/bench --site working install-app flexible_budget --force
2025-04-19 18:57:55,000 INFO /home/<USER>/.local/bin/bench migrate
2025-04-19 21:25:35,474 INFO /home/<USER>/.local/bin/bench migrate
2025-04-19 21:46:11,750 INFO /home/<USER>/.local/bin/bench migrate
2025-04-19 21:53:12,055 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-04-19 21:53:21,473 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-04-19 21:53:28,442 INFO /home/<USER>/.local/bin/bench migrate
2025-04-20 18:00:02,094 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-21 09:37:42,957 INFO /home/<USER>/.local/bin/bench start
2025-04-21 09:37:43,255 INFO /home/<USER>/.local/bin/bench worker
2025-04-21 09:37:43,258 INFO /home/<USER>/.local/bin/bench schedule
2025-04-21 09:37:43,279 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-21 09:37:43,293 INFO /home/<USER>/.local/bin/bench watch
2025-04-21 12:00:01,397 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-21 12:44:38,512 INFO /home/<USER>/.local/bin/bench migrate
2025-04-21 12:45:42,434 INFO /home/<USER>/.local/bin/bench --site working clear-cache
2025-04-21 12:45:52,488 INFO /home/<USER>/.local/bin/bench --site working clear-website-cache
2025-04-21 18:00:01,647 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-22 08:33:45,533 INFO /home/<USER>/.local/bin/bench start
2025-04-22 08:33:45,849 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-22 08:33:45,851 INFO /home/<USER>/.local/bin/bench schedule
2025-04-22 08:33:45,870 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 08:33:45,871 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 12:00:01,787 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-22 18:00:01,596 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-22 20:28:06,102 INFO /home/<USER>/.local/bin/bench start
2025-04-22 20:28:06,382 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 20:28:06,384 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-22 20:28:06,392 INFO /home/<USER>/.local/bin/bench schedule
2025-04-22 20:28:06,402 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 20:52:11,668 INFO /home/<USER>/.local/bin/bench use explore
2025-04-22 20:52:17,682 INFO /home/<USER>/.local/bin/bench start
2025-04-22 20:52:17,943 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-22 20:52:17,950 INFO /home/<USER>/.local/bin/bench schedule
2025-04-22 20:52:17,960 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 20:52:17,971 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 20:52:28,783 INFO /home/<USER>/.local/bin/bench migrate
2025-04-22 21:36:58,321 INFO /home/<USER>/.local/bin/bench use working
2025-04-22 21:37:04,183 INFO /home/<USER>/.local/bin/bench start
2025-04-22 21:37:04,458 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 21:37:04,460 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 21:37:04,487 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-22 21:37:04,493 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 11:58:38,955 INFO /home/<USER>/.local/bin/bench start
2025-04-23 11:58:39,266 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 11:58:39,273 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 11:58:39,276 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 11:58:39,276 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-23 12:00:01,621 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-23 18:00:01,821 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-24 08:32:02,868 INFO /home/<USER>/.local/bin/bench start
2025-04-24 08:32:03,201 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 08:32:03,211 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 08:32:03,212 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 08:32:03,214 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 08:52:26,374 INFO /home/<USER>/.local/bin/bench use explore
2025-04-24 08:52:32,009 INFO /home/<USER>/.local/bin/bench start
2025-04-24 08:52:32,286 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 08:52:32,301 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 08:52:32,317 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 08:52:32,346 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 10:20:39,165 INFO /home/<USER>/.local/bin/bench --site explore --force restore 20250424_030038-dev15-fms_aakvaerp_com-database.sql.gz
2025-04-24 10:22:58,353 INFO /home/<USER>/.local/bin/bench --site explore install-app clearing
2025-04-24 10:23:06,308 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 10:27:33,155 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/vsd_fleet_ms.git
2025-04-24 10:27:33,163 LOG Getting vsd_fleet_ms
2025-04-24 10:27:33,163 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/vsd_fleet_ms.git  --depth 1 --origin upstream
2025-04-24 10:27:35,540 LOG Installing vsd_fleet_ms
2025-04-24 10:27:35,541 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms 
2025-04-24 10:27:37,373 DEBUG bench build --app vsd_fleet_ms
2025-04-24 10:27:37,525 INFO /home/<USER>/.local/bin/bench build --app vsd_fleet_ms
2025-04-24 10:27:44,147 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-24 10:27:44,328 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-24 10:27:44,328 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-24 10:27:44,683 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-24 10:29:02,451 INFO /home/<USER>/.local/bin/bench --site explore install-app vsd_fleet_ms
2025-04-24 10:29:12,390 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 10:29:34,910 INFO /home/<USER>/.local/bin/bench start
2025-04-24 10:29:35,183 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 10:29:35,183 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 10:29:35,185 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 10:29:35,212 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 10:29:41,363 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:03:25,577 INFO /home/<USER>/.local/bin/bench --site explore set-admin-password aakvatech
2025-04-24 11:06:54,387 INFO /home/<USER>/.local/bin/bench --site explore install-app icd_tz
2025-04-24 11:07:16,387 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:10:15,726 INFO /home/<USER>/.local/bin/bench --site explore install-app payments
2025-04-24 11:10:24,836 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms
2025-04-24 11:11:03,614 INFO /home/<USER>/.local/bin/bench --site explore install-app hrms --force
2025-04-24 11:16:48,559 INFO /home/<USER>/.local/bin/bench use working
2025-04-24 11:16:54,689 INFO /home/<USER>/.local/bin/bench start
2025-04-24 11:16:54,945 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 11:16:54,945 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 11:16:54,986 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 11:16:54,986 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 11:19:40,776 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:25:21,717 INFO /home/<USER>/.local/bin/bench use explore
2025-04-24 11:25:26,989 INFO /home/<USER>/.local/bin/bench start
2025-04-24 11:25:27,249 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 11:25:27,259 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 11:25:27,282 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 11:25:27,289 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 11:25:39,055 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:29:02,862 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:36:49,603 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:50:13,958 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:50:50,828 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:53:10,933 INFO /home/<USER>/.local/bin/bench --site explore clear-cache
2025-04-24 11:53:22,988 INFO /home/<USER>/.local/bin/bench --site explore clear-website-cache
2025-04-24 11:58:21,416 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 11:59:02,101 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-04-24 12:00:01,778 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-24 15:39:30,191 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 15:56:49,262 INFO /home/<USER>/.local/bin/bench --site explore --force restore 20250424_000150-icd-dev_aakvaerp_com-database.sql.gz
2025-04-24 15:58:28,851 INFO /home/<USER>/.local/bin/bench --site explore set-admin-password aakvatech
2025-04-24 16:00:07,126 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/vfd_providers.git
2025-04-24 16:00:07,135 LOG Getting vfd_providers
2025-04-24 16:00:07,135 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/vfd_providers.git  --depth 1 --origin upstream
2025-04-24 16:00:08,857 LOG Installing vfd_providers
2025-04-24 16:00:08,858 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-04-24 16:00:11,033 DEBUG bench build --app vfd_providers
2025-04-24 16:00:11,226 INFO /home/<USER>/.local/bin/bench build --app vfd_providers
2025-04-24 16:00:18,108 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-04-24 16:00:18,279 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-24 16:00:18,279 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-24 16:00:18,668 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-24 16:01:02,205 INFO /home/<USER>/.local/bin/bench --site explore install-app vfd_providers
2025-04-24 16:01:22,827 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 16:01:32,209 INFO /home/<USER>/.local/bin/bench start
2025-04-24 16:01:32,470 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 16:01:32,478 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 16:01:32,512 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 16:01:32,518 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 16:01:38,365 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 16:12:00,451 INFO /home/<USER>/.local/bin/bench use working
2025-04-24 16:12:15,086 INFO /home/<USER>/.local/bin/bench start
2025-04-24 16:12:15,374 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 16:12:15,398 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 16:12:15,408 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 16:12:15,429 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 17:02:57,638 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 18:00:01,456 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-24 18:11:13,471 INFO /home/<USER>/.local/bin/bench migrate
2025-04-24 21:35:12,565 INFO /home/<USER>/.local/bin/bench start
2025-04-24 21:35:12,846 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 21:35:12,867 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 21:35:12,882 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 21:35:12,891 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 21:36:10,419 INFO /home/<USER>/.local/bin/bench use explore
2025-04-24 21:36:15,649 INFO /home/<USER>/.local/bin/bench start
2025-04-24 21:36:15,891 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 21:36:15,896 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 21:36:15,905 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 21:36:15,949 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 21:57:20,451 INFO /home/<USER>/.local/bin/bench use working
2025-04-24 21:57:25,934 INFO /home/<USER>/.local/bin/bench start
2025-04-24 21:57:26,195 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 21:57:26,197 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-24 21:57:26,238 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 21:57:26,239 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 22:37:14,572 INFO /home/<USER>/.local/bin/bench use explore
2025-04-25 08:24:05,803 INFO /home/<USER>/.local/bin/bench start
2025-04-25 08:24:06,112 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 08:24:06,131 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 08:24:06,139 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 08:24:06,140 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-25 09:30:28,439 INFO /home/<USER>/.local/bin/bench migrate
2025-04-25 11:15:21,397 INFO /home/<USER>/.local/bin/bench use working
2025-04-25 11:15:26,513 INFO /home/<USER>/.local/bin/bench start
2025-04-25 11:15:26,879 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-25 11:15:26,887 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 11:15:26,906 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 11:15:26,909 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 11:41:15,936 INFO /home/<USER>/.local/bin/bench migrate
2025-04-25 12:00:01,837 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-25 18:00:02,247 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-26 11:51:45,727 INFO /home/<USER>/.local/bin/bench use explore
2025-04-26 11:51:51,964 INFO /home/<USER>/.local/bin/bench start
2025-04-26 11:51:52,239 INFO /home/<USER>/.local/bin/bench schedule
2025-04-26 11:51:52,240 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-26 11:51:52,255 INFO /home/<USER>/.local/bin/bench watch
2025-04-26 11:51:52,262 INFO /home/<USER>/.local/bin/bench worker
2025-04-26 12:00:01,961 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-26 18:00:01,462 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-26 20:15:40,264 INFO /home/<USER>/.local/bin/bench start
2025-04-26 20:15:40,583 INFO /home/<USER>/.local/bin/bench watch
2025-04-26 20:15:40,588 INFO /home/<USER>/.local/bin/bench worker
2025-04-26 20:15:40,589 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-26 20:15:40,593 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 11:11:53,841 INFO /home/<USER>/.local/bin/bench start
2025-04-27 11:11:54,103 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 11:11:54,106 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-27 11:11:54,110 INFO /home/<USER>/.local/bin/bench worker
2025-04-27 11:11:54,125 INFO /home/<USER>/.local/bin/bench watch
2025-04-27 12:00:01,359 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-27 18:00:02,194 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-28 08:39:09,729 INFO /home/<USER>/.local/bin/bench use explore
2025-04-28 08:39:17,842 INFO /home/<USER>/.local/bin/bench start
2025-04-28 08:39:18,135 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-28 08:39:18,148 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 08:39:18,179 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 08:39:18,182 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 09:51:03,709 INFO /home/<USER>/.local/bin/bench use working
2025-04-28 09:51:10,176 INFO /home/<USER>/.local/bin/bench start
2025-04-28 09:51:10,446 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 09:51:10,448 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 09:51:10,448 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 09:51:10,476 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-28 12:00:01,762 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-28 14:43:15,041 INFO /home/<USER>/.local/bin/bench use working
2025-04-28 14:43:19,993 INFO /home/<USER>/.local/bin/bench start
2025-04-28 14:43:20,303 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 14:43:20,310 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 14:43:20,317 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-28 14:43:20,333 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 18:00:01,443 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-28 20:38:49,091 INFO /home/<USER>/.local/bin/bench start
2025-04-28 20:38:49,366 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 20:38:49,412 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 20:38:49,412 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 20:38:49,416 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-28 20:39:52,356 INFO /home/<USER>/.local/bin/bench use explore
2025-04-28 20:39:58,873 INFO /home/<USER>/.local/bin/bench start
2025-04-28 20:39:59,147 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 20:39:59,151 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 20:39:59,173 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-28 20:39:59,189 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 08:52:51,484 INFO /home/<USER>/.local/bin/bench use working
2025-04-29 08:52:56,535 INFO /home/<USER>/.local/bin/bench start
2025-04-29 08:52:56,843 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-29 08:52:56,856 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 08:52:56,870 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 08:52:56,884 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 12:00:01,941 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-29 16:37:45,556 INFO /home/<USER>/.local/bin/bench migrate
2025-04-29 18:00:01,530 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-30 08:55:41,469 INFO /home/<USER>/.local/bin/bench start
2025-04-30 08:55:41,756 INFO /home/<USER>/.local/bin/bench worker
2025-04-30 08:55:41,764 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-04-30 08:55:41,796 INFO /home/<USER>/.local/bin/bench schedule
2025-04-30 08:55:41,804 INFO /home/<USER>/.local/bin/bench watch
2025-04-30 12:00:01,350 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-30 18:00:02,046 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-01 12:00:02,024 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-01 13:59:56,708 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-01 13:59:56,986 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-01 13:59:56,989 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-01 13:59:57,010 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-01 13:59:57,017 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-01 18:00:01,964 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-02 08:40:53,596 INFO /home/<USER>/anaconda3/bin/bench use working
2025-05-02 08:41:06,104 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-02 08:41:06,365 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-02 08:41:06,372 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-02 08:41:06,411 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-02 08:41:06,422 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-02 12:00:01,957 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-02 18:00:01,630 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-02 18:17:24,998 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-02 18:17:25,619 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-02 18:17:25,630 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-02 18:17:25,645 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-02 18:17:25,666 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-03 12:00:01,809 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-03 18:00:01,877 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-04 12:00:01,721 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-05 09:25:33,213 INFO /home/<USER>/.local/bin/bench use working
2025-05-05 09:25:38,959 INFO /home/<USER>/.local/bin/bench start
2025-05-05 09:25:39,234 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-05 09:25:39,255 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 09:25:39,257 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 09:25:39,267 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 09:58:56,407 INFO /home/<USER>/.local/bin/bench migrate
2025-05-05 10:02:27,520 INFO /home/<USER>/.local/bin/bench migrate
2025-05-05 12:00:01,667 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-05 18:00:01,333 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-05 22:15:21,574 INFO /home/<USER>/.local/bin/bench start
2025-05-05 22:15:21,875 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 22:15:21,875 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-05 22:15:21,911 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 22:15:21,924 INFO /home/<USER>/.local/bin/bench worker
2025-05-06 09:14:33,359 INFO /home/<USER>/.local/bin/bench use working
2025-05-06 09:14:42,480 INFO /home/<USER>/.local/bin/bench start
2025-05-06 09:14:42,803 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-06 09:14:42,804 INFO /home/<USER>/.local/bin/bench schedule
2025-05-06 09:14:42,812 INFO /home/<USER>/.local/bin/bench watch
2025-05-06 09:14:42,823 INFO /home/<USER>/.local/bin/bench worker
2025-05-06 12:00:02,167 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-06 16:07:14,706 INFO /home/<USER>/.local/bin/bench new-site massumin
2025-05-06 16:13:07,892 INFO /home/<USER>/.local/bin/bench --site massumin restore 20250506_053009-masumin14-av_frappe_cloud-database.sql.gz
2025-05-06 16:13:55,229 INFO /home/<USER>/.local/bin/bench get-app erpnext --branch version-15
2025-05-06 16:14:16,371 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-06 16:14:34,942 INFO /home/<USER>/.local/bin/bench use massumin
2025-05-06 16:14:48,865 INFO /home/<USER>/.local/bin/bench get-app erpnext --branch version-15
2025-05-06 16:15:01,366 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-06 16:15:21,914 INFO /home/<USER>/.local/bin/bench --site massumin install-app erpnext
2025-05-06 16:17:10,759 INFO /home/<USER>/.local/bin/bench start
2025-05-06 16:17:11,121 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-06 16:17:11,122 INFO /home/<USER>/.local/bin/bench watch
2025-05-06 16:17:11,140 INFO /home/<USER>/.local/bin/bench schedule
2025-05-06 16:17:11,145 INFO /home/<USER>/.local/bin/bench worker
2025-05-06 16:19:44,808 INFO /home/<USER>/.local/bin/bench --site massumin install-app payments
2025-05-06 16:19:56,522 INFO /home/<USER>/.local/bin/bench --site massumin install-app hrms
2025-05-06 16:20:54,434 INFO /home/<USER>/.local/bin/bench --site massumin install-app csf_tz
2025-05-06 16:23:24,906 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/Payware.git
2025-05-06 16:23:24,915 LOG Getting Payware
2025-05-06 16:23:24,915 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/Payware.git  --depth 1 --origin upstream
2025-05-06 16:23:27,120 LOG Installing payware
2025-05-06 16:23:27,121 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payware 
2025-05-06 16:23:29,573 DEBUG bench build --app payware
2025-05-06 16:23:29,724 INFO /home/<USER>/.local/bin/bench build --app payware
2025-05-06 16:23:36,293 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-05-06 16:23:36,472 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-06 16:23:36,472 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-06 16:23:36,798 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-06 16:23:52,343 INFO /home/<USER>/.local/bin/bench --site massumin install-app vfd_providers
2025-05-06 16:24:07,957 INFO /home/<USER>/.local/bin/bench --site massumin install-app payware
2025-05-06 16:24:22,924 INFO /home/<USER>/.local/bin/bench migrate
2025-05-06 16:24:38,600 INFO /home/<USER>/.local/bin/bench start
2025-05-06 16:24:38,880 INFO /home/<USER>/.local/bin/bench watch
2025-05-06 16:24:38,880 INFO /home/<USER>/.local/bin/bench worker
2025-05-06 16:24:38,897 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-06 16:24:38,907 INFO /home/<USER>/.local/bin/bench schedule
2025-05-06 16:24:44,756 INFO /home/<USER>/.local/bin/bench migrate
2025-05-06 16:25:17,103 INFO /home/<USER>/.local/bin/bench --site massumin restore 20250506_053009-masumin14-av_frappe_cloud-database.sql.gz
2025-05-06 16:35:02,484 INFO /home/<USER>/.local/bin/bench --site massumin set-admin-password aakvatech
2025-05-06 18:00:02,115 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-06 18:21:34,981 INFO /home/<USER>/.local/bin/bench use working
2025-05-07 00:00:01,798 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-07 12:00:01,878 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-07 12:27:03,714 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-07 12:27:03,989 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-07 12:27:04,000 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-07 12:27:04,024 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-07 12:27:04,027 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-07 17:34:22,053 INFO /home/<USER>/.local/bin/bench use explore
2025-05-07 17:34:26,432 INFO /home/<USER>/.local/bin/bench start
2025-05-07 17:34:26,751 INFO /home/<USER>/.local/bin/bench schedule
2025-05-07 17:34:26,752 INFO /home/<USER>/.local/bin/bench worker
2025-05-07 17:34:26,757 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-07 17:34:26,765 INFO /home/<USER>/.local/bin/bench watch
2025-05-07 18:00:01,703 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-07 21:22:20,951 INFO /home/<USER>/.local/bin/bench use explore
2025-05-07 21:22:25,692 INFO /home/<USER>/.local/bin/bench start
2025-05-07 21:22:25,985 INFO /home/<USER>/.local/bin/bench schedule
2025-05-07 21:22:25,988 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-07 21:22:25,988 INFO /home/<USER>/.local/bin/bench worker
2025-05-07 21:22:26,002 INFO /home/<USER>/.local/bin/bench watch
2025-05-08 00:00:01,440 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-08 00:32:36,850 INFO /home/<USER>/.local/bin/bench console
2025-05-08 09:08:31,626 INFO /home/<USER>/.local/bin/bench start
2025-05-08 09:08:31,915 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-08 09:08:31,939 INFO /home/<USER>/.local/bin/bench worker
2025-05-08 09:08:31,956 INFO /home/<USER>/.local/bin/bench watch
2025-05-08 09:08:31,966 INFO /home/<USER>/.local/bin/bench schedule
2025-05-08 11:25:20,914 INFO /home/<USER>/.local/bin/bench migrate
2025-05-08 11:35:11,846 INFO /home/<USER>/.local/bin/bench migrate
2025-05-08 12:00:01,427 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-08 18:00:02,192 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-09 09:32:19,743 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-09 09:32:20,051 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-09 09:32:20,059 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-09 09:32:20,063 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-09 09:32:20,077 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-09 12:00:01,812 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-09 18:00:01,336 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-10 09:03:00,335 INFO /home/<USER>/.local/bin/bench use working
2025-05-10 09:04:10,234 INFO /home/<USER>/.local/bin/bench start
2025-05-10 09:04:10,514 INFO /home/<USER>/.local/bin/bench schedule
2025-05-10 09:04:10,528 INFO /home/<USER>/.local/bin/bench worker
2025-05-10 09:04:10,528 INFO /home/<USER>/.local/bin/bench watch
2025-05-10 09:04:10,538 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-10 12:00:01,367 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-10 18:00:02,017 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-11 12:00:01,350 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-11 18:00:01,524 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-12 09:05:18,327 INFO /home/<USER>/.local/bin/bench use explore
2025-05-12 09:05:24,182 INFO /home/<USER>/.local/bin/bench start
2025-05-12 09:05:24,515 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-12 09:05:24,517 INFO /home/<USER>/.local/bin/bench watch
2025-05-12 09:05:24,519 INFO /home/<USER>/.local/bin/bench schedule
2025-05-12 09:05:24,526 INFO /home/<USER>/.local/bin/bench worker
2025-05-12 12:00:01,296 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-12 12:58:58,252 INFO /home/<USER>/.local/bin/bench use working
2025-05-12 12:59:04,351 INFO /home/<USER>/.local/bin/bench start
2025-05-12 12:59:04,623 INFO /home/<USER>/.local/bin/bench watch
2025-05-12 12:59:04,636 INFO /home/<USER>/.local/bin/bench schedule
2025-05-12 12:59:04,662 INFO /home/<USER>/.local/bin/bench worker
2025-05-12 12:59:04,665 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-12 14:10:54,264 INFO /home/<USER>/.local/bin/bench use explore
2025-05-12 14:11:57,857 INFO /home/<USER>/.local/bin/bench start
2025-05-12 14:11:58,182 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-12 14:11:58,200 INFO /home/<USER>/.local/bin/bench watch
2025-05-12 14:11:58,213 INFO /home/<USER>/.local/bin/bench schedule
2025-05-12 14:11:58,251 INFO /home/<USER>/.local/bin/bench worker
2025-05-12 15:45:27,668 INFO /home/<USER>/.local/bin/bench use working
2025-05-12 15:45:33,716 INFO /home/<USER>/.local/bin/bench start
2025-05-12 15:45:34,046 INFO /home/<USER>/.local/bin/bench watch
2025-05-12 15:45:34,065 INFO /home/<USER>/.local/bin/bench schedule
2025-05-12 15:45:34,066 INFO /home/<USER>/.local/bin/bench worker
2025-05-12 15:45:34,081 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-12 18:00:02,290 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-13 12:00:02,094 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-13 18:00:01,673 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-14 09:20:16,371 INFO /home/<USER>/.local/bin/bench new-site sdg_esg
2025-05-14 09:22:05,367 INFO /home/<USER>/.local/bin/bench --site sdg_esg install-app erpnext
2025-05-14 09:27:15,879 INFO /home/<USER>/.local/bin/bench new-app SDG Reporting
2025-05-14 09:27:15,887 WARNING /home/<USER>/.local/bin/bench new-app SDG Reporting executed with exit code 2
2025-05-14 09:27:16,302 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-05-14 09:28:04,459 INFO /home/<USER>/.local/bin/bench new-app SDG_Reporting
2025-05-14 09:28:04,463 LOG creating new app sdg_reporting
2025-05-14 09:29:32,471 LOG Installing sdg_reporting
2025-05-14 09:29:32,476 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting 
2025-05-14 09:29:35,322 DEBUG bench build --app sdg_reporting
2025-05-14 09:29:35,482 INFO /home/<USER>/.local/bin/bench build --app sdg_reporting
2025-05-14 09:29:44,518 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-05-14 09:29:44,694 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-14 09:29:44,694 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-14 09:29:45,037 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-05-14 09:30:02,921 INFO /home/<USER>/.local/bin/bench use sdg_esg
2025-05-14 09:30:07,840 INFO /home/<USER>/.local/bin/bench start
2025-05-14 09:30:08,133 INFO /home/<USER>/.local/bin/bench schedule
2025-05-14 09:30:08,147 INFO /home/<USER>/.local/bin/bench worker
2025-05-14 09:30:08,156 INFO /home/<USER>/.local/bin/bench watch
2025-05-14 09:30:08,158 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-14 09:32:26,218 INFO /home/<USER>/.local/bin/bench --site sdg_esg install-app sdg_reporting
2025-05-14 09:32:39,852 INFO /home/<USER>/.local/bin/bench migrate
2025-05-14 12:00:01,663 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-14 18:00:02,056 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-14 19:07:13,858 INFO /home/<USER>/.local/bin/bench use massumin
2025-05-14 19:07:18,043 INFO /home/<USER>/.local/bin/bench start
2025-05-14 19:07:18,332 INFO /home/<USER>/.local/bin/bench watch
2025-05-14 19:07:18,346 INFO /home/<USER>/.local/bin/bench schedule
2025-05-14 19:07:18,354 INFO /home/<USER>/.local/bin/bench worker
2025-05-14 19:07:18,356 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-14 19:16:43,279 INFO /home/<USER>/.local/bin/bench migrate
2025-05-15 08:52:40,880 INFO /home/<USER>/.local/bin/bench use explore
2025-05-15 08:52:48,685 INFO /home/<USER>/.local/bin/bench start
2025-05-15 08:52:49,021 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-15 08:52:49,025 INFO /home/<USER>/.local/bin/bench worker
2025-05-15 08:52:49,035 INFO /home/<USER>/.local/bin/bench schedule
2025-05-15 08:52:49,043 INFO /home/<USER>/.local/bin/bench watch
2025-05-15 12:00:01,822 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-15 18:00:01,775 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-16 09:30:19,478 INFO /home/<USER>/.local/bin/bench use explore
2025-05-16 09:30:28,894 INFO /home/<USER>/.local/bin/bench start
2025-05-16 09:30:29,177 INFO /home/<USER>/.local/bin/bench watch
2025-05-16 09:30:29,179 INFO /home/<USER>/.local/bin/bench worker
2025-05-16 09:30:29,208 INFO /home/<USER>/.local/bin/bench schedule
2025-05-16 09:30:29,219 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-16 09:40:18,799 INFO /home/<USER>/.local/bin/bench migrate
2025-05-16 11:59:21,423 INFO /home/<USER>/.local/bin/bench start
2025-05-16 11:59:21,737 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-16 11:59:21,745 INFO /home/<USER>/.local/bin/bench worker
2025-05-16 11:59:21,748 INFO /home/<USER>/.local/bin/bench watch
2025-05-16 11:59:21,776 INFO /home/<USER>/.local/bin/bench schedule
2025-05-16 12:00:02,027 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-16 17:20:29,120 INFO /home/<USER>/.local/bin/bench use sdg_esg
2025-05-16 17:20:41,201 INFO /home/<USER>/.local/bin/bench start
2025-05-16 17:20:41,504 INFO /home/<USER>/.local/bin/bench worker
2025-05-16 17:20:41,504 INFO /home/<USER>/.local/bin/bench schedule
2025-05-16 17:20:41,511 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-16 17:20:41,520 INFO /home/<USER>/.local/bin/bench watch
2025-05-16 18:00:01,576 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-16 18:04:04,368 INFO /home/<USER>/.local/bin/bench new-site sdg
2025-05-16 20:56:03,777 INFO /home/<USER>/.local/bin/bench use sdg
2025-05-16 20:56:10,145 INFO /home/<USER>/.local/bin/bench start
2025-05-16 20:56:10,437 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-16 20:56:10,441 INFO /home/<USER>/.local/bin/bench watch
2025-05-16 20:56:10,447 INFO /home/<USER>/.local/bin/bench worker
2025-05-16 20:56:10,450 INFO /home/<USER>/.local/bin/bench schedule
2025-05-16 20:57:50,530 INFO /home/<USER>/.local/bin/bench new-app SDG
2025-05-16 20:57:50,533 LOG creating new app sdg
2025-05-16 20:57:51,019 WARNING /home/<USER>/.local/bin/bench new-app SDG executed with exit code 1
2025-05-16 20:57:52,171 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-05-16 20:58:28,322 INFO /home/<USER>/.local/bin/bench new-app sdg_tz
2025-05-16 20:58:28,326 LOG creating new app sdg_tz
2025-05-16 20:59:19,390 LOG Installing sdg_tz
2025-05-16 20:59:19,394 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/sdg_tz 
2025-05-16 20:59:21,551 DEBUG bench build --app sdg_tz
2025-05-16 20:59:21,696 INFO /home/<USER>/.local/bin/bench build --app sdg_tz
2025-05-16 20:59:30,456 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-05-16 20:59:30,631 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-16 20:59:30,631 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-16 20:59:31,464 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-05-16 21:01:57,204 INFO /home/<USER>/.local/bin/bench --site sdg sdg_reporting
2025-05-16 21:02:09,057 INFO /home/<USER>/.local/bin/bench --site sdg install-app sdg_reporting
2025-05-16 21:02:19,171 INFO /home/<USER>/.local/bin/bench migrate
2025-05-16 21:02:32,012 INFO /home/<USER>/.local/bin/bench start
2025-05-16 21:02:32,283 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-16 21:02:32,293 INFO /home/<USER>/.local/bin/bench schedule
2025-05-16 21:02:32,308 INFO /home/<USER>/.local/bin/bench worker
2025-05-16 21:02:32,336 INFO /home/<USER>/.local/bin/bench watch
2025-05-16 21:02:38,377 INFO /home/<USER>/.local/bin/bench migrate
2025-05-17 12:00:01,660 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-17 13:01:04,613 INFO /home/<USER>/.local/bin/bench use working
2025-05-17 13:01:09,016 INFO /home/<USER>/.local/bin/bench start
2025-05-17 13:01:09,345 INFO /home/<USER>/.local/bin/bench worker
2025-05-17 13:01:09,357 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-05-17 13:01:09,362 INFO /home/<USER>/.local/bin/bench watch
2025-05-17 13:01:09,377 INFO /home/<USER>/.local/bin/bench schedule
2025-05-17 18:00:02,190 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-18 18:00:02,008 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-19 08:56:00,310 INFO /home/<USER>/anaconda3/bin/bench use explore
2025-05-19 08:56:06,104 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-19 08:56:06,389 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-19 08:56:06,396 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-19 08:56:06,408 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-19 08:56:06,411 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-19 11:31:07,263 INFO /home/<USER>/anaconda3/bin/bench get-app webshop --branch version-15
2025-05-19 11:31:08,221 LOG Getting webshop
2025-05-19 11:31:08,221 DEBUG cd ./apps && git clone https://github.com/frappe/webshop.git --branch version-15 --depth 1 --origin upstream
2025-05-19 11:31:10,945 LOG Installing webshop
2025-05-19 11:31:10,946 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/webshop 
2025-05-19 11:31:14,054 DEBUG bench build --app webshop
2025-05-19 11:31:14,209 INFO /home/<USER>/anaconda3/bin/bench build --app webshop
2025-05-19 11:31:23,481 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-05-19 11:31:23,680 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-19 11:31:23,680 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-19 11:31:23,857 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-19 11:31:57,801 INFO /home/<USER>/anaconda3/bin/bench --site working install-app webshop
2025-05-19 11:32:35,125 INFO /home/<USER>/anaconda3/bin/bench use working
2025-05-19 11:32:41,079 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-19 11:32:41,361 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-19 11:32:41,374 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-19 11:32:41,384 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-19 11:32:41,388 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-19 12:00:01,534 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-19 12:10:27,315 INFO /home/<USER>/anaconda3/bin/bench --site explore restore 20250519_114929-silverentertrade14-sf-av_frappe_cloud-database.sql.gz
2025-05-19 13:01:57,176 INFO /home/<USER>/anaconda3/bin/bench --site explore set-admin-password aakvatech
2025-05-19 13:02:07,995 INFO /home/<USER>/anaconda3/bin/bench use explore
2025-05-19 13:02:15,716 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-19 13:02:16,148 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-19 13:02:16,165 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-19 13:02:16,173 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-19 13:02:16,180 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-19 13:02:31,821 INFO /home/<USER>/anaconda3/bin/bench migrate
2025-05-19 14:52:47,690 INFO /home/<USER>/anaconda3/bin/bench migrate
2025-05-19 18:00:01,867 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-20 09:18:30,257 INFO /home/<USER>/anaconda3/bin/bench use explore
2025-05-20 09:18:36,050 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-20 09:18:36,342 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-20 09:18:36,346 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-20 09:18:36,351 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-20 09:18:36,356 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-20 12:00:01,347 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-20 15:52:25,043 INFO /home/<USER>/anaconda3/bin/bench use explore
2025-05-20 15:52:48,617 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-20 15:53:38,480 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-20 15:53:38,836 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-20 15:53:38,841 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-20 15:53:38,842 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-20 15:53:38,858 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-20 15:58:48,586 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-20 15:58:48,909 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-20 15:58:48,910 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-20 15:58:48,917 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-20 15:58:48,961 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-20 16:00:10,625 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-20 16:00:10,930 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-20 16:00:10,930 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-20 16:00:10,947 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-20 16:00:10,955 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-20 16:02:02,491 INFO /home/<USER>/anaconda3/bin/bench migrate
2025-05-20 18:00:01,595 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-21 10:01:24,921 INFO /home/<USER>/anaconda3/bin/bench use explore
2025-05-21 10:01:30,197 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-21 10:01:30,507 INFO /home/<USER>/anaconda3/bin/bench worker
2025-05-21 10:01:30,512 INFO /home/<USER>/anaconda3/bin/bench watch
2025-05-21 10:01:30,515 INFO /home/<USER>/anaconda3/bin/bench serve --port 8003
2025-05-21 10:01:30,517 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-05-21 10:03:06,444 INFO /home/<USER>/anaconda3/bin/bench migrate
2025-05-21 11:22:20,024 INFO /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git
2025-05-21 11:22:20,033 LOG Getting HMS_TZ
2025-05-21 11:22:20,034 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git  --depth 1 --origin upstream
2025-05-21 11:22:23,424 LOG Installing hms_tz
2025-05-21 11:22:23,424 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-21 11:22:25,573 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz  executed with exit code 1
2025-05-21 11:22:25,574 WARNING /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git executed with exit code 1
2025-05-21 11:22:25,852 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-21 11:23:19,218 INFO /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git
2025-05-21 11:23:19,228 LOG Getting HMS_TZ
2025-05-21 11:23:19,228 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git  --depth 1 --origin upstream
2025-05-21 11:23:22,766 WARNING /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git executed with exit code 1
2025-05-21 11:23:23,483 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-21 11:25:04,027 INFO /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git
2025-05-21 11:25:04,041 LOG Getting HMS_TZ
2025-05-21 11:25:04,041 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git  --depth 1 --origin upstream
2025-05-21 11:25:06,632 WARNING /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git executed with exit code 1
2025-05-21 11:25:07,277 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-21 11:27:54,401 INFO /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git
2025-05-21 11:27:54,410 LOG Getting HMS_TZ
2025-05-21 11:27:54,410 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git  --depth 1 --origin upstream
2025-05-21 11:27:57,221 LOG Installing hms_tz
2025-05-21 11:27:57,221 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-21 11:27:58,566 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz  executed with exit code 1
2025-05-21 11:27:58,566 WARNING /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git executed with exit code 1
2025-05-21 11:27:58,850 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-21 11:37:07,930 INFO /home/<USER>/anaconda3/bin/bench get-app healthcare
2025-05-21 11:37:09,157 LOG Getting healthcare
2025-05-21 11:37:09,158 DEBUG cd ./apps && git clone https://github.com/frappe/healthcare.git  --depth 1 --origin upstream
2025-05-21 11:37:12,078 LOG Installing healthcare
2025-05-21 11:37:12,078 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-05-21 11:37:15,597 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -r ./apps/healthcare/dev-requirements.txt
2025-05-21 11:37:23,204 DEBUG bench build --app healthcare
2025-05-21 11:37:23,351 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app healthcare
2025-05-21 11:37:30,197 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 11:37:30,370 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 11:37:30,371 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-21 11:37:30,550 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-21 11:37:37,292 INFO /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git
2025-05-21 11:37:37,301 LOG Getting HMS_TZ
2025-05-21 11:37:37,301 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git  --depth 1 --origin upstream
2025-05-21 11:37:40,007 LOG Installing hms_tz
2025-05-21 11:37:40,007 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-21 11:37:45,596 DEBUG bench build --app hms_tz
2025-05-21 11:37:45,789 WARNING bench build --app hms_tz executed with exit code 1
2025-05-21 11:37:45,789 WARNING /home/<USER>/anaconda3/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git executed with exit code 1
2025-05-21 11:37:46,460 INFO A newer version of bench is available: 5.24.1 → 5.25.1
2025-05-21 11:42:07,385 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --resolve-deps
2025-05-21 11:42:09,258 LOG Getting HMS_TZ
2025-05-21 11:42:09,258 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git  --depth 1 --origin upstream
2025-05-21 11:42:12,177 LOG Installing hms_tz
2025-05-21 11:42:12,178 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-21 11:42:18,441 DEBUG bench build --app hms_tz
2025-05-21 11:42:18,608 WARNING bench build --app hms_tz executed with exit code 1
2025-05-21 11:42:18,608 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --resolve-deps executed with exit code 1
2025-05-21 11:49:34,197 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app hms_tz
2025-05-21 11:51:43,147 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore install-app hms_tz
2025-05-21 11:52:30,105 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore install-app healthcare
2025-05-21 11:56:45,078 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore remove-from-installed-apps healthcare
2025-05-21 11:58:27,127 INFO /home/<USER>/anaconda3/bin/bench use working
2025-05-21 11:58:34,171 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-21 11:58:34,406 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-21 11:58:34,416 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-21 11:58:34,438 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-21 11:58:34,443 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-21 11:58:43,875 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-21 11:59:43,149 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app healthcare
2025-05-21 12:00:02,217 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-21 12:00:38,103 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app hms_tz
2025-05-21 12:02:04,553 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working remove-from-installed-apps healthcare
2025-05-21 12:02:12,784 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app hms_tz --force
2025-05-21 12:02:34,408 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working remove-from-installed-apps hms_tz
2025-05-21 12:02:39,288 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app hms_tz --force
2025-05-21 12:02:48,097 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working remove-from-installed-apps hms_tz
2025-05-21 12:03:00,802 INFO /home/<USER>/anaconda3/bin/bench use explore
2025-05-21 12:03:05,306 INFO /home/<USER>/anaconda3/bin/bench start
2025-05-21 12:03:05,561 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-21 12:03:05,563 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-21 12:03:05,571 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-21 12:03:05,573 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-21 12:09:51,569 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore remove-from-installed-apps hms_tz
2025-05-21 12:09:58,800 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-21 12:11:08,667 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --branch pr_mergify
2025-05-21 12:11:08,678 LOG Getting HMS_TZ
2025-05-21 12:11:08,678 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git --branch pr_mergify --depth 1 --origin upstream
2025-05-21 12:11:11,851 LOG Installing hms_tz
2025-05-21 12:11:11,855 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-21 12:11:18,635 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && yarn install --check-files
2025-05-21 12:11:27,943 DEBUG bench build --app hms_tz
2025-05-21 12:11:28,230 WARNING bench build --app hms_tz executed with exit code 1
2025-05-21 12:11:28,230 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --branch pr_mergify executed with exit code 1
2025-05-21 12:22:40,575 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app hms_tz
2025-05-21 12:23:19,534 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site install-app hms_tz
2025-05-21 12:23:41,664 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore install-app hms_tz
2025-05-21 12:24:03,678 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore install-app hms_tz --force
2025-05-21 12:27:38,439 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench update
2025-05-21 12:27:42,249 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-05-21 12:32:14,039 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench update executed with exit code 1
2025-05-21 12:51:35,147 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench update
2025-05-21 12:51:37,085 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-05-21 12:55:32,666 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench update executed with exit code 1
2025-05-21 18:00:01,978 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-22 00:00:01,929 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-22 12:00:01,453 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-23 08:44:21,792 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-23 08:44:22,072 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-23 08:44:22,073 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-23 08:44:22,074 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-23 08:44:22,080 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-23 09:02:04,329 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-05-23 09:02:15,402 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-23 09:02:15,749 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-23 09:02:15,763 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-23 09:02:15,768 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-23 09:02:15,770 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-23 09:04:15,930 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench update
2025-05-23 09:04:17,874 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-05-23 09:06:40,321 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench update executed with exit code 1
2025-05-23 09:08:47,113 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench update --reset
2025-05-23 09:10:52,960 LOG pulling frappe
2025-05-23 09:10:52,961 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:10:54,793 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git reset --hard upstream/version-15
2025-05-23 09:10:54,880 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git reflog expire --all
2025-05-23 09:10:54,928 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git gc --prune=all
2025-05-23 09:11:10,409 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && find . -name "*.pyc" -delete
2025-05-23 09:11:10,491 LOG pulling icd_tz
2025-05-23 09:11:10,491 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git fetch --depth=1 --no-tags upstream feat-v2
2025-05-23 09:11:12,610 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git reset --hard upstream/feat-v2
2025-05-23 09:11:12,613 WARNING cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git reset --hard upstream/feat-v2 executed with exit code 128
2025-05-23 09:11:12,613 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench update --reset executed with exit code 1
2025-05-23 09:14:28,461 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench update --reset
2025-05-23 09:16:27,309 LOG pulling frappe
2025-05-23 09:16:27,309 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:16:29,433 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git reset --hard upstream/version-15
2025-05-23 09:16:29,573 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git reflog expire --all
2025-05-23 09:16:29,608 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && git gc --prune=all
2025-05-23 09:16:37,891 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && find . -name "*.pyc" -delete
2025-05-23 09:16:37,961 LOG pulling icd_tz
2025-05-23 09:16:37,962 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git fetch --depth=1 --no-tags upstream main
2025-05-23 09:16:39,868 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git reset --hard upstream/main
2025-05-23 09:16:39,874 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git reflog expire --all
2025-05-23 09:16:39,889 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && git gc --prune=all
2025-05-23 09:16:40,061 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/icd_tz && find . -name "*.pyc" -delete
2025-05-23 09:16:40,069 LOG pulling wiki
2025-05-23 09:16:40,070 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && git fetch --depth=1 --no-tags upstream master
2025-05-23 09:16:42,480 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && git reset --hard upstream/master
2025-05-23 09:16:42,618 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && git reflog expire --all
2025-05-23 09:16:42,665 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && git gc --prune=all
2025-05-23 09:16:43,184 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && find . -name "*.pyc" -delete
2025-05-23 09:16:43,201 LOG pulling csf_tz
2025-05-23 09:16:43,201 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:16:44,988 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && git reset --hard upstream/version-15
2025-05-23 09:16:45,014 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && git reflog expire --all
2025-05-23 09:16:45,021 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && git gc --prune=all
2025-05-23 09:16:45,558 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && find . -name "*.pyc" -delete
2025-05-23 09:16:45,594 LOG pulling lms
2025-05-23 09:16:45,595 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:16:49,923 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && git reset --hard upstream/develop
2025-05-23 09:16:49,987 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && git reflog expire --all
2025-05-23 09:16:50,002 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && git gc --prune=all
2025-05-23 09:16:52,410 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && find . -name "*.pyc" -delete
2025-05-23 09:16:52,618 LOG pulling sdg_reporting
2025-05-23 09:16:52,618 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting && git fetch --depth=1 --no-tags origin develop
2025-05-23 09:16:54,174 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting && git reset --hard origin/develop
2025-05-23 09:16:54,187 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting && git reflog expire --all
2025-05-23 09:16:54,201 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting && git gc --prune=all
2025-05-23 09:16:54,326 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting && find . -name "*.pyc" -delete
2025-05-23 09:16:54,338 LOG pulling hms_tz
2025-05-23 09:16:54,339 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && git fetch --depth=1 --no-tags upstream pr_mergify
2025-05-23 09:16:55,823 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && git reset --hard upstream/pr_mergify
2025-05-23 09:16:55,840 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && git reflog expire --all
2025-05-23 09:16:55,843 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && git gc --prune=all
2025-05-23 09:16:55,910 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && find . -name "*.pyc" -delete
2025-05-23 09:16:55,962 LOG pulling vsd_fleet_ms
2025-05-23 09:16:55,962 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms && git fetch --depth=1 --no-tags upstream master
2025-05-23 09:16:57,431 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms && git reset --hard upstream/master
2025-05-23 09:16:57,466 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms && git reflog expire --all
2025-05-23 09:16:57,473 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms && git gc --prune=all
2025-05-23 09:16:57,522 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms && find . -name "*.pyc" -delete
2025-05-23 09:16:57,553 LOG pulling vfd_providers
2025-05-23 09:16:57,553 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vfd_providers && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:16:59,023 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vfd_providers && git reset --hard upstream/develop
2025-05-23 09:16:59,029 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vfd_providers && git reflog expire --all
2025-05-23 09:16:59,035 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vfd_providers && git gc --prune=all
2025-05-23 09:16:59,080 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/vfd_providers && find . -name "*.pyc" -delete
2025-05-23 09:16:59,094 LOG pulling webshop
2025-05-23 09:16:59,094 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/webshop && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:17:00,795 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/webshop && git reset --hard upstream/version-15
2025-05-23 09:17:00,859 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/webshop && git reflog expire --all
2025-05-23 09:17:00,864 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/webshop && git gc --prune=all
2025-05-23 09:17:00,930 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/webshop && find . -name "*.pyc" -delete
2025-05-23 09:17:00,957 LOG pulling education
2025-05-23 09:17:00,957 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/education && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:17:03,341 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/education && git reset --hard upstream/version-15
2025-05-23 09:17:03,380 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/education && git reflog expire --all
2025-05-23 09:17:03,384 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/education && git gc --prune=all
2025-05-23 09:17:03,420 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/education && find . -name "*.pyc" -delete
2025-05-23 09:17:03,436 LOG pulling payware
2025-05-23 09:17:03,436 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payware && git fetch --depth=1 --no-tags upstream additional-salary-patch1
2025-05-23 09:17:04,945 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payware && git reset --hard upstream/additional-salary-patch1
2025-05-23 09:17:04,960 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payware && git reflog expire --all
2025-05-23 09:17:04,966 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payware && git gc --prune=all
2025-05-23 09:17:05,005 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payware && find . -name "*.pyc" -delete
2025-05-23 09:17:05,020 LOG pulling payments
2025-05-23 09:17:05,020 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payments && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:17:06,589 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payments && git reset --hard upstream/version-15
2025-05-23 09:17:06,597 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payments && git reflog expire --all
2025-05-23 09:17:06,602 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payments && git gc --prune=all
2025-05-23 09:17:06,651 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/payments && find . -name "*.pyc" -delete
2025-05-23 09:17:06,660 LOG pulling healthcare
2025-05-23 09:17:06,661 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/healthcare && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:17:08,201 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/healthcare && git reset --hard upstream/develop
2025-05-23 09:17:08,267 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/healthcare && git reflog expire --all
2025-05-23 09:17:08,270 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/healthcare && git gc --prune=all
2025-05-23 09:17:08,311 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/healthcare && find . -name "*.pyc" -delete
2025-05-23 09:17:08,329 LOG pulling wcfcb_zm
2025-05-23 09:17:08,329 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:17:10,921 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm && git reset --hard upstream/develop
2025-05-23 09:17:10,930 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm && git reflog expire --all
2025-05-23 09:17:10,938 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm && git gc --prune=all
2025-05-23 09:17:10,974 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm && find . -name "*.pyc" -delete
2025-05-23 09:17:10,980 LOG pulling school
2025-05-23 09:17:10,981 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/school && git fetch --depth=1 --no-tags origin main
2025-05-23 09:17:12,522 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/school && git reset --hard origin/main
2025-05-23 09:17:12,531 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/school && git reflog expire --all
2025-05-23 09:17:12,539 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/school && git gc --prune=all
2025-05-23 09:17:12,632 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/school && find . -name "*.pyc" -delete
2025-05-23 09:17:12,645 LOG pulling hrms
2025-05-23 09:17:12,645 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:17:18,960 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && git reset --hard upstream/develop
2025-05-23 09:17:19,024 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && git reflog expire --all
2025-05-23 09:17:19,088 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && git gc --prune=all
2025-05-23 09:17:20,883 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && find . -name "*.pyc" -delete
2025-05-23 09:17:21,176 LOG pulling clearing
2025-05-23 09:17:21,176 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/clearing && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:17:22,774 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/clearing && git reset --hard upstream/develop
2025-05-23 09:17:22,786 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/clearing && git reflog expire --all
2025-05-23 09:17:22,804 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/clearing && git gc --prune=all
2025-05-23 09:17:22,983 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/clearing && find . -name "*.pyc" -delete
2025-05-23 09:17:22,993 LOG pulling erpnext
2025-05-23 09:17:22,993 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && git fetch --depth=1 --no-tags upstream version-15
2025-05-23 09:17:43,681 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && git reset --hard upstream/version-15
2025-05-23 09:17:43,932 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && git reflog expire --all
2025-05-23 09:17:43,988 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && git gc --prune=all
2025-05-23 09:18:10,952 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && find . -name "*.pyc" -delete
2025-05-23 09:18:11,612 LOG pulling drive
2025-05-23 09:18:11,612 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && git fetch --depth=1 --no-tags upstream main
2025-05-23 09:18:15,574 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && git reset --hard upstream/main
2025-05-23 09:18:15,705 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && git reflog expire --all
2025-05-23 09:18:15,715 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && git gc --prune=all
2025-05-23 09:18:15,956 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && find . -name "*.pyc" -delete
2025-05-23 09:18:16,947 LOG pulling flexible_budget
2025-05-23 09:18:16,947 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/flexible_budget && git fetch --depth=1 --no-tags upstream develop
2025-05-23 09:18:19,641 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/flexible_budget && git reset --hard upstream/develop
2025-05-23 09:18:19,651 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/flexible_budget && git reflog expire --all
2025-05-23 09:18:19,660 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/flexible_budget && git gc --prune=all
2025-05-23 09:18:19,710 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/flexible_budget && find . -name "*.pyc" -delete
2025-05-23 09:18:19,715 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-05-23 09:18:21,012 LOG Installing frappe
2025-05-23 09:18:21,014 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-05-23 09:18:53,629 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-05-23 09:19:09,510 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-05-23 09:19:20,582 LOG Installing icd_tz
2025-05-23 09:19:20,583 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/icd_tz 
2025-05-23 09:19:25,523 LOG Installing wiki
2025-05-23 09:19:25,524 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wiki 
2025-05-23 09:19:28,711 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && yarn install --check-files
2025-05-23 09:19:29,707 LOG Installing csf_tz
2025-05-23 09:19:29,708 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-05-23 09:20:02,089 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-05-23 09:20:02,687 LOG Installing lms
2025-05-23 09:20:02,687 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lms 
2025-05-23 09:20:07,281 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/lms && yarn install --check-files
2025-05-23 09:21:18,948 LOG Installing sdg_reporting
2025-05-23 09:21:18,949 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/sdg_reporting 
2025-05-23 09:21:22,499 LOG Installing hms_tz
2025-05-23 09:21:22,505 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-23 09:21:42,772 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && yarn install --check-files
2025-05-23 09:21:45,203 LOG Installing sdg_tz
2025-05-23 09:21:45,203 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/sdg_tz 
2025-05-23 09:21:48,233 LOG Installing vsd_fleet_ms
2025-05-23 09:21:48,233 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms 
2025-05-23 09:21:55,552 LOG Installing vfd_providers
2025-05-23 09:21:55,553 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-05-23 09:22:03,063 LOG Installing webshop
2025-05-23 09:22:03,063 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/webshop 
2025-05-23 09:22:06,503 LOG Installing renting
2025-05-23 09:22:06,504 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/renting 
2025-05-23 09:22:10,109 LOG Installing education
2025-05-23 09:22:10,110 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/education 
2025-05-23 09:22:17,741 LOG Installing payware
2025-05-23 09:22:17,741 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payware 
2025-05-23 09:22:41,070 LOG Installing payments
2025-05-23 09:22:41,070 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payments 
2025-05-23 09:22:47,578 LOG Installing healthcare
2025-05-23 09:22:47,578 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-05-23 09:22:51,135 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -r /home/<USER>/Desktop/frappe-bench/apps/healthcare/dev-requirements.txt
2025-05-23 09:22:52,515 LOG Installing wcfcb_zm
2025-05-23 09:22:52,516 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wcfcb_zm 
2025-05-23 09:22:56,265 LOG Installing school
2025-05-23 09:22:56,265 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/school 
2025-05-23 09:23:00,080 LOG Installing hrms
2025-05-23 09:23:00,081 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-05-23 09:23:03,810 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-05-23 09:23:56,663 LOG Installing clearing
2025-05-23 09:23:56,663 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/clearing 
2025-05-23 09:24:00,941 LOG Installing erpnext
2025-05-23 09:24:00,941 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext 
2025-05-23 09:24:04,763 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && yarn install --check-files
2025-05-23 09:24:05,240 LOG Installing drive
2025-05-23 09:24:05,241 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/drive 
2025-05-23 09:24:38,726 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/drive && yarn install --check-files
2025-05-23 09:27:47,574 LOG Installing flexible_budget
2025-05-23 09:27:47,575 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-05-23 09:27:51,916 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench update --reset executed with exit code 1
2025-05-23 09:30:57,228 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-23 09:30:57,528 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-23 09:30:57,537 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-23 09:30:57,551 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-23 09:30:57,584 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-23 09:31:10,991 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-23 09:32:38,863 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-05-23 09:32:43,540 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-23 09:32:43,819 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-23 09:32:43,876 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-23 09:32:43,878 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-23 09:32:43,879 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-23 09:34:09,635 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-23 09:34:09,945 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-23 09:34:09,953 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-23 09:34:09,958 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-23 09:34:09,975 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-23 12:00:02,135 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-23 18:00:01,840 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-24 12:00:01,982 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-24 18:00:01,674 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-25 12:00:01,951 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-25 18:00:02,216 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-26 08:51:34,561 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-26 08:51:41,098 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-26 08:51:41,381 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-26 08:51:41,401 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-26 08:51:41,406 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-26 08:51:41,418 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-26 08:53:20,249 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-26 08:56:14,026 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working reinstall
2025-05-26 09:26:50,743 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working restore 20250526_030114-dev15-fms_aakvaerp_com-database.sql.gz
2025-05-26 09:29:00,125 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working set-admin-password aakvatech
2025-05-26 09:29:29,478 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-26 09:29:37,459 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-26 09:29:37,724 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-26 09:29:37,731 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-26 09:29:37,748 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-26 09:29:37,749 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-26 12:00:01,951 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-26 18:00:01,742 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-27 08:48:18,525 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-27 08:48:18,800 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-27 08:48:18,811 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-27 08:48:18,819 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-27 08:48:18,822 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-27 08:48:50,994 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-27 08:48:57,577 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-27 08:48:57,825 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-27 08:48:57,840 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-27 08:48:57,846 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-27 08:48:57,853 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-27 12:00:01,230 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-27 17:49:59,638 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-27 18:00:01,574 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-28 08:40:39,983 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-28 08:40:45,203 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-28 08:40:45,498 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-28 08:40:45,502 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-28 08:40:45,504 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-28 08:40:45,505 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-28 12:00:01,877 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-28 15:56:23,808 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-28 15:56:29,959 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-28 15:56:30,213 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-28 15:56:30,213 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-28 15:56:30,236 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-28 15:56:30,246 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-28 16:02:35,724 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app csf_tz
2025-05-28 16:03:11,120 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app csf_tz --force
2025-05-28 16:47:03,033 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working install-app csf_tz --force
2025-05-28 18:00:01,743 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-29 12:00:01,950 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-29 18:00:01,858 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-30 12:00:01,902 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-30 18:00:02,245 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-30 22:49:39,704 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --branch v15_nhif_test
2025-05-30 22:49:39,715 LOG Getting HMS_TZ
2025-05-30 22:49:39,715 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git --branch v15_nhif_test --depth 1 --origin upstream
2025-05-30 22:49:47,904 LOG Installing hms_tz
2025-05-30 22:49:47,907 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-05-30 22:50:29,921 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && yarn install --check-files
2025-05-30 22:50:36,510 DEBUG bench build --app hms_tz
2025-05-30 22:50:36,634 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app hms_tz
2025-05-30 22:50:47,725 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-05-30 22:50:47,899 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-30 22:50:47,899 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-30 22:52:45,005 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-05-30 22:53:31,681 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental install-app hms_tz
2025-05-30 22:53:46,711 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-30 22:53:47,083 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-30 22:53:47,086 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-30 22:53:47,096 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-30 22:53:47,098 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-30 22:55:24,947 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-30 22:56:37,485 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-30 22:56:37,793 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-30 22:56:37,816 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-30 22:56:37,826 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-30 22:56:37,833 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-30 22:57:28,691 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-30 22:57:33,795 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-30 22:57:34,086 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-30 22:57:34,118 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-30 22:57:34,130 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-30 22:57:34,137 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-30 22:59:24,414 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-30 23:07:18,980 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-30 23:07:19,235 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-30 23:07:19,242 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-30 23:07:19,247 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-30 23:07:19,264 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-30 23:07:31,344 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-30 23:08:40,885 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-05-30 23:08:48,304 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-30 23:08:48,654 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-30 23:08:48,656 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-30 23:08:48,671 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-30 23:08:48,676 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 00:00:01,577 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-31 09:24:59,048 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use woking
2025-05-31 09:25:05,471 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 09:25:05,737 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 09:25:05,749 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 09:25:05,762 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 09:25:05,766 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 09:26:31,838 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-31 09:26:37,417 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 09:26:37,678 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 09:26:37,680 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 09:26:37,693 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 09:26:37,694 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 14:34:45,879 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-31 14:34:52,086 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 14:34:52,390 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 14:34:52,406 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 14:34:52,409 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 14:34:52,433 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 14:36:02,167 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-05-31 14:36:07,254 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 14:36:07,520 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 14:36:07,531 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 14:36:07,532 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 14:36:07,551 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 14:45:14,239 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-31 14:55:49,230 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental install-app healthcare
2025-05-31 15:11:20,100 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-05-31 15:48:49,661 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-31 15:48:56,246 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 15:48:56,532 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 15:48:56,554 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 15:48:56,554 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 15:48:56,559 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 18:00:02,160 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-31 18:59:13,481 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-05-31 18:59:28,697 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 18:59:28,966 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 18:59:28,971 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 18:59:28,999 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 18:59:29,015 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 19:03:22,812 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-05-31 19:03:27,556 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 19:03:27,782 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 19:03:27,804 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 19:03:27,819 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-05-31 19:03:27,824 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 19:22:14,875 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-05-31 19:22:24,576 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-05-31 19:22:24,865 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-05-31 19:22:24,898 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-05-31 19:22:24,914 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-05-31 19:22:24,919 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-01 00:00:01,776 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-01 18:00:02,508 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-02 09:28:57,682 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-02 09:28:57,962 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-02 09:28:57,971 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-02 09:28:57,977 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-02 09:28:57,984 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-02 09:29:07,371 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-02 09:29:13,279 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-02 09:29:13,545 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-02 09:29:13,554 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-02 09:29:13,569 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-02 09:29:13,569 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-02 09:29:57,985 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental restore 20241101_000002-nephron1_com-database.sql.gz
2025-06-02 09:34:43,141 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental set-admin-password aakvatech
2025-06-02 09:34:49,166 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 09:38:56,762 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --branch v14_nhif_test
2025-06-02 09:38:56,771 LOG Getting HMS_TZ
2025-06-02 09:38:56,771 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git --branch v14_nhif_test --depth 1 --origin upstream
2025-06-02 09:38:59,727 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --branch v14_nhif_test executed with exit code 1
2025-06-02 09:39:50,038 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/HMS_TZ.git --branch v14_nhif_test
2025-06-02 09:39:50,047 LOG Getting HMS_TZ
2025-06-02 09:39:50,047 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/HMS_TZ.git --branch v14_nhif_test --depth 1 --origin upstream
2025-06-02 09:39:52,627 LOG Installing hms_tz
2025-06-02 09:39:52,631 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-06-02 09:40:06,640 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hms_tz && yarn install --check-files
2025-06-02 09:40:09,069 DEBUG bench build --app hms_tz
2025-06-02 09:40:09,287 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app hms_tz
2025-06-02 09:40:17,873 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-02 09:40:18,058 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-02 09:40:18,058 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-02 09:40:56,901 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental install-app hms_tz
2025-06-02 09:41:10,737 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental install-app hms_tz --force
2025-06-02 09:43:07,003 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 09:43:35,034 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental reinstall
2025-06-02 09:50:33,833 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 10:03:09,677 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 10:14:06,989 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 10:25:24,630 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site ental clear-cache
2025-06-02 10:25:36,994 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental clear-cache
2025-06-02 10:25:49,134 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental clear-website-cache
2025-06-02 10:34:54,540 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental restore 20241101_000002-nephron1_com-database.sql.gz
2025-06-02 10:36:29,672 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin install-app healthcare
2025-06-02 10:36:49,691 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 10:37:48,982 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin install-app hms_tz
2025-06-02 10:38:41,421 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin migrate
2025-06-02 10:42:44,439 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 10:43:03,223 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin migrate
2025-06-02 10:45:39,741 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental reinstall
2025-06-02 10:46:04,613 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental reinstall
2025-06-02 10:54:41,716 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use massumin
2025-06-02 10:54:47,719 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-02 10:54:48,094 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-02 10:54:48,100 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-02 10:54:48,106 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-02 10:54:48,118 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-02 11:15:28,210 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/marley.git --branch version-14
2025-06-02 11:15:28,219 LOG Getting marley
2025-06-02 11:15:28,219 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/marley.git --branch version-14 --depth 1 --origin upstream
2025-06-02 11:15:39,940 LOG Installing healthcare
2025-06-02 11:15:39,940 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-06-02 11:15:45,265 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -r ./apps/healthcare/dev-requirements.txt
2025-06-02 11:15:46,879 DEBUG bench build --app healthcare
2025-06-02 11:15:47,010 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app healthcare
2025-06-02 11:15:54,508 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-02 11:15:54,709 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-02 11:15:54,709 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-02 11:16:38,304 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental install-app healthcare --force
2025-06-02 11:17:08,604 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental migrate
2025-06-02 11:18:10,191 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-02 11:18:15,952 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-02 11:18:16,297 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-02 11:18:16,308 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-02 11:18:16,322 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-02 11:18:16,331 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-02 11:28:09,942 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental restore 20241101_000002-nephron1_com-database.sql.gz
2025-06-02 11:30:14,411 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin install-app healthcare --force
2025-06-02 11:30:33,163 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-02 11:31:10,950 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin migrate
2025-06-02 11:34:12,497 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental set-admin-password aakvatech
2025-06-02 12:00:02,050 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-02 17:48:39,448 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-02 17:48:45,109 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-02 17:48:45,348 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-02 17:48:45,368 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-02 17:48:45,381 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-02 17:48:45,393 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-02 17:49:57,618 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-02 17:50:03,154 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-02 17:50:03,438 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-02 17:50:03,438 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-02 17:50:03,447 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-02 17:50:03,466 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-02 18:00:01,712 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-02 18:16:44,113 INFO /home/<USER>/.local/bin/bench use rental
2025-06-02 18:16:51,251 INFO /home/<USER>/.local/bin/bench start
2025-06-02 18:16:51,530 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-02 18:16:51,542 INFO /home/<USER>/.local/bin/bench schedule
2025-06-02 18:16:51,556 INFO /home/<USER>/.local/bin/bench worker
2025-06-02 18:16:51,571 INFO /home/<USER>/.local/bin/bench watch
2025-06-02 18:29:40,632 INFO /home/<USER>/.local/bin/bench start
2025-06-02 18:29:40,887 INFO /home/<USER>/.local/bin/bench watch
2025-06-02 18:29:40,890 INFO /home/<USER>/.local/bin/bench schedule
2025-06-02 18:29:40,892 INFO /home/<USER>/.local/bin/bench worker
2025-06-02 18:29:40,919 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-03 00:00:01,802 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-03 08:23:31,665 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-03 08:23:36,629 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 08:23:36,929 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 08:23:36,929 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 08:23:36,940 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 08:23:36,943 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 09:43:00,277 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-03 09:43:10,190 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 09:43:10,451 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 09:43:10,454 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 09:43:10,463 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 09:43:10,463 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 09:53:42,490 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-03 09:53:50,474 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 09:53:50,765 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 09:53:50,770 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 09:53:50,784 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 09:53:50,786 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 10:19:42,318 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-03 10:19:47,171 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 10:19:47,439 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 10:19:47,445 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 10:19:47,449 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 10:19:47,469 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 10:30:32,675 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 10:30:32,939 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 10:30:32,948 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 10:30:32,969 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 10:30:32,971 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 12:00:01,363 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-03 12:49:51,389 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-03 12:49:56,095 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 12:49:56,358 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 12:49:56,363 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 12:49:56,371 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 12:49:56,373 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 12:53:57,864 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-03 15:20:23,696 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore restore 20250602_221508-silverentertrade14-sf-av_frappe_cloud-database.sql.gz
2025-06-03 15:32:44,323 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-03 15:32:48,869 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 15:32:49,177 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 15:32:49,190 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 15:32:49,192 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 15:32:49,196 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 16:08:26,994 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 16:08:27,298 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 16:08:27,300 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 16:08:27,307 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 16:08:27,323 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 16:59:36,634 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-03 16:59:45,348 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 16:59:45,754 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 16:59:45,755 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 16:59:45,757 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 16:59:45,758 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 17:02:13,962 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore set-admin-password aakvatech
2025-06-03 17:37:13,044 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-03 17:37:18,059 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 17:37:18,412 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 17:37:18,413 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 17:37:18,429 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 17:37:18,450 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 17:38:21,705 INFO /home/<USER>/.local/bin/bench start
2025-06-03 17:38:22,000 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-03 17:38:22,053 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 17:38:22,054 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 17:38:22,064 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 17:45:17,674 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-03 17:45:24,766 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 17:46:20,556 INFO /home/<USER>/.local/bin/bench use rental
2025-06-03 17:46:24,406 INFO /home/<USER>/.local/bin/bench start
2025-06-03 17:46:24,704 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 17:46:24,707 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-03 17:46:24,750 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 17:46:24,753 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 17:49:31,679 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-03 17:49:36,489 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-03 17:49:36,748 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-03 17:49:36,765 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-03 17:49:36,770 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-03 17:49:36,770 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-03 17:50:15,143 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-03 17:54:51,613 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working migrate
2025-06-03 17:57:01,462 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working migrate
2025-06-03 17:58:58,368 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-03 17:59:26,006 INFO /home/<USER>/.local/bin/bench start
2025-06-03 17:59:26,313 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 17:59:26,319 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 17:59:26,333 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-03 17:59:26,363 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 18:00:01,913 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-03 19:03:34,955 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-03 19:11:58,117 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 11:03:05,562 INFO /home/<USER>/.local/bin/bench start
2025-06-04 11:03:05,942 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-04 11:03:05,953 INFO /home/<USER>/.local/bin/bench watch
2025-06-04 11:03:05,980 INFO /home/<USER>/.local/bin/bench schedule
2025-06-04 11:03:05,992 INFO /home/<USER>/.local/bin/bench worker
2025-06-04 11:18:07,322 INFO /home/<USER>/.local/bin/bench use rental
2025-06-04 11:18:21,967 INFO /home/<USER>/.local/bin/bench start
2025-06-04 11:18:22,252 INFO /home/<USER>/.local/bin/bench watch
2025-06-04 11:18:22,259 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-04 11:18:22,292 INFO /home/<USER>/.local/bin/bench worker
2025-06-04 11:18:22,299 INFO /home/<USER>/.local/bin/bench schedule
2025-06-04 12:00:01,733 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-04 12:37:11,382 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-06-04 12:37:16,626 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 12:37:52,199 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 12:37:52,430 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-04 12:37:52,436 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-04 12:37:52,474 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-04 12:37:52,475 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-04 18:00:02,236 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-04 21:16:22,331 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-06-04 21:16:48,740 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-04 21:16:53,997 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 21:16:54,261 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-04 21:16:54,276 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-04 21:16:54,281 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-04 21:16:54,291 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-04 21:17:59,345 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 21:17:59,595 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-04 21:17:59,609 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-04 21:17:59,621 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-04 21:17:59,623 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-04 21:19:16,600 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg_esg
2025-06-04 21:19:21,934 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 21:19:22,181 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-04 21:19:22,195 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-04 21:19:22,205 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-04 21:19:22,206 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-04 21:20:37,506 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-04 21:20:40,552 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-04 21:20:40,816 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-04 21:20:40,819 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-04 21:20:40,832 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-04 21:20:40,832 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-05 09:47:54,845 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-05 09:47:55,127 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-05 09:47:55,142 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-05 09:47:55,145 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-05 09:47:55,148 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-05 10:07:18,507 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-06-05 10:07:24,186 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-05 10:07:24,445 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-05 10:07:24,458 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-05 10:07:24,469 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-05 10:07:24,474 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-05 11:55:01,255 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg_esg
2025-06-05 11:55:06,330 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-05 11:55:06,606 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-05 11:55:06,607 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-05 11:55:06,608 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-05 11:55:06,608 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-05 11:55:47,198 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-05 11:55:51,977 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-05 11:55:52,232 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-05 11:55:52,238 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-05 11:55:52,242 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-05 11:55:52,243 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-05 12:00:01,877 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-05 17:46:25,367 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site icd.localhost console
2025-06-05 17:46:39,969 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all list-apps
2025-06-05 17:47:15,401 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site working list-apps
2025-06-05 17:47:25,419 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site sdg list-apps
2025-06-05 17:47:33,514 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin list-apps
2025-06-05 17:48:13,833 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore list-apps
2025-06-05 17:48:27,783 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-05 17:51:43,013 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-05 17:58:21,893 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-05 18:00:01,514 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-05 18:04:14,214 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-05 18:16:32,939 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-05 18:27:53,332 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-05 22:11:26,782 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore console
2025-06-06 12:00:02,279 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-06 18:00:02,178 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-08 12:00:02,054 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-08 18:00:02,009 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-09 12:00:01,772 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-09 12:41:35,488 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg_esg
2025-06-09 12:41:42,968 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-09 12:41:43,249 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-09 12:41:43,250 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-09 12:41:43,258 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-09 12:41:43,262 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-09 12:43:46,339 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-09 12:43:46,603 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-09 12:43:46,604 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-09 12:43:46,616 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-09 12:43:46,626 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-09 12:44:40,882 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-09 12:44:46,135 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-09 12:44:46,415 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-09 12:44:46,416 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-09 12:44:46,422 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-09 12:44:46,431 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-09 18:00:01,614 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-10 12:00:01,914 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-10 18:00:02,097 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-11 00:00:01,938 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-11 12:00:02,228 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-11 18:00:01,576 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-12 08:23:36,122 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-06-12 08:23:44,028 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 08:23:44,281 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 08:23:44,288 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 08:23:44,299 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 08:23:44,304 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 08:30:21,744 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-06-12 08:30:21,753 LOG Getting csf_tz
2025-06-12 08:30:21,753 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-06-12 08:30:25,589 LOG Installing csf_tz
2025-06-12 08:30:25,589 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-06-12 08:30:56,438 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-06-12 08:30:58,069 DEBUG bench build --app csf_tz
2025-06-12 08:30:58,209 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app csf_tz
2025-06-12 08:31:07,668 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 08:31:07,848 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 08:31:07,848 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 08:31:40,356 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site install-app csf_tz
2025-06-12 08:32:03,135 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore install-app csf_tz
2025-06-12 08:32:14,235 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore install-app csf_tz --force
2025-06-12 08:33:29,589 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 12:00:01,656 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-12 12:33:03,404 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/servicems.git --branch version-15
2025-06-12 12:33:03,414 LOG Getting servicems
2025-06-12 12:33:03,414 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/servicems.git --branch version-15 --depth 1 --origin upstream
2025-06-12 12:33:11,447 LOG Installing servicems
2025-06-12 12:33:11,447 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/servicems 
2025-06-12 12:33:28,069 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/servicems && yarn install --check-files
2025-06-12 12:34:16,212 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site mmt.dev
2025-06-12 12:34:35,531 DEBUG bench build --app servicems
2025-06-12 12:34:35,678 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app servicems
2025-06-12 12:41:35,353 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site mmt.dev restore 20250612_100159-mmt-av_frappe_cloud-database.sql.gz
2025-06-12 12:44:00,508 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site mmt.dev set-admin-password aakvatech
2025-06-12 12:44:13,217 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use mmt.dev
2025-06-12 12:44:18,253 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 12:44:18,576 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 12:44:18,589 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 12:44:18,595 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 12:44:18,599 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 12:44:29,150 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 12:44:29,518 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 12:44:29,518 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 12:45:26,002 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 12:49:13,134 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site wasco.dev
2025-06-12 12:50:46,616 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site wasco.dev restore 20250612_023007-wasco-av_frappe_cloud-database.sql.gz
2025-06-12 12:58:51,572 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use wasco.dev
2025-06-12 12:58:56,575 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 12:58:56,902 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 12:58:56,914 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 12:58:56,928 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 12:58:56,931 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 12:59:13,193 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 13:00:35,009 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/biometric-client.git
2025-06-12 13:00:35,021 LOG Getting biometric-client
2025-06-12 13:00:35,021 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/biometric-client.git  --depth 1 --origin upstream
2025-06-12 13:00:37,218 LOG Installing biometric_client
2025-06-12 13:00:37,218 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/biometric_client 
2025-06-12 13:00:40,584 DEBUG bench build --app biometric_client
2025-06-12 13:00:40,791 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app biometric_client
2025-06-12 13:00:48,536 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 13:00:48,774 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 13:00:48,774 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 13:01:00,983 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 13:01:09,302 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 13:01:09,686 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 13:01:09,698 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 13:01:09,710 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 13:01:09,710 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 13:01:15,223 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 13:04:52,094 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site wasco.dev set-admin-password aakvatech
2025-06-12 13:12:17,205 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 13:12:17,453 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 13:12:17,458 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 13:12:17,464 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 13:12:17,481 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 14:18:45,163 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/staff_loans.git
2025-06-12 14:18:45,172 LOG Getting staff_loans
2025-06-12 14:18:45,173 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/staff_loans.git  --depth 1 --origin upstream
2025-06-12 14:18:47,355 LOG Installing staff_loans
2025-06-12 14:18:47,355 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/staff_loans 
2025-06-12 14:18:52,032 DEBUG bench build --app staff_loans
2025-06-12 14:18:52,182 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app staff_loans
2025-06-12 14:18:59,010 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 14:18:59,197 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 14:18:59,197 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 14:18:59,842 INFO A newer version of bench is available: 5.25.1 → 5.25.3
2025-06-12 14:19:49,621 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site beetle
2025-06-12 14:21:50,631 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use beetle
2025-06-12 14:22:44,050 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site beetle restore 20250611_134516-beetle-av_frappe_cloud-database.sql.gz
2025-06-12 14:25:50,299 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site beetle set-admin-password aakvatech
2025-06-12 14:26:57,567 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 14:26:57,895 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 14:26:57,907 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 14:26:57,914 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 14:26:57,917 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 14:28:17,612 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 14:29:06,266 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/erpnext_telegram.git
2025-06-12 14:29:06,299 LOG Getting erpnext_telegram
2025-06-12 14:29:06,300 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/erpnext_telegram.git  --depth 1 --origin upstream
2025-06-12 14:29:08,303 LOG Installing erpnext_telegram_integration
2025-06-12 14:29:08,304 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext_telegram_integration 
2025-06-12 14:29:28,827 DEBUG bench build --app erpnext_telegram_integration
2025-06-12 14:29:29,314 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app erpnext_telegram_integration
2025-06-12 14:29:41,849 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 14:29:42,558 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 14:29:42,558 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 14:29:43,512 INFO A newer version of bench is available: 5.25.1 → 5.25.3
2025-06-12 14:29:55,076 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-12 14:32:44,660 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-12 14:32:45,027 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-12 14:32:45,040 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-12 14:32:45,060 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-12 14:32:45,081 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-12 16:31:45,453 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-12 16:32:18,424 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental restore 20250612_000224-shm-dev15_aakvaerp_com-database.sql.gz
2025-06-12 17:34:30,616 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app non-profit
2025-06-12 17:34:32,314 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app non-profit executed with exit code 1
2025-06-12 17:34:32,517 INFO A newer version of bench is available: 5.25.1 → 5.25.4
2025-06-12 17:34:56,037 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Aakvatech-Limited/non_profit.git
2025-06-12 17:34:56,046 LOG Getting non_profit
2025-06-12 17:34:56,046 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/non_profit.git  --depth 1 --origin upstream
2025-06-12 17:34:58,121 LOG Installing non_profit
2025-06-12 17:34:58,122 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/non_profit 
2025-06-12 17:35:03,476 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -r ./apps/non_profit/dev-requirements.txt
2025-06-12 17:35:04,441 DEBUG bench build --app non_profit
2025-06-12 17:35:04,572 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app non_profit
2025-06-12 17:35:36,440 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 17:35:36,653 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 17:35:36,653 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 17:35:36,936 INFO A newer version of bench is available: 5.25.1 → 5.25.4
2025-06-12 17:36:23,394 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Aakvatech-Limited/edu_tz.git
2025-06-12 17:36:23,403 LOG Getting edu_tz
2025-06-12 17:36:23,403 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/edu_tz.git  --depth 1 --origin upstream
2025-06-12 17:36:24,927 LOG Installing edu_tz
2025-06-12 17:36:24,927 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/edu_tz 
2025-06-12 17:36:37,915 DEBUG bench build --app edu_tz
2025-06-12 17:36:38,037 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app edu_tz
2025-06-12 17:36:40,083 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-12 17:36:40,320 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-12 17:36:40,320 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-12 17:36:40,653 INFO A newer version of bench is available: 5.25.1 → 5.25.4
2025-06-12 18:00:01,264 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-13 00:00:02,176 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-13 08:51:19,798 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-13 08:51:31,423 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-13 08:51:31,705 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-13 08:51:31,711 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-13 08:51:31,715 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-13 08:51:31,717 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-13 08:51:41,504 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-13 09:11:02,613 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Emmafidelis/PropMS.git
2025-06-13 09:11:02,623 LOG Getting PropMS
2025-06-13 09:11:02,623 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/PropMS.git  --depth 1 --origin upstream
2025-06-13 09:11:04,735 LOG Installing propms
2025-06-13 09:11:04,736 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/propms 
2025-06-13 09:11:20,179 DEBUG bench build --app propms
2025-06-13 09:11:20,327 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app propms
2025-06-13 10:07:23,081 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-13 10:07:23,269 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-13 10:07:23,270 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-13 10:07:23,559 INFO A newer version of bench is available: 5.25.1 → 5.25.4
2025-06-13 10:07:27,020 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-13 10:07:36,609 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-13 10:07:46,851 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-13 10:07:47,119 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-13 10:07:47,120 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-13 10:07:47,130 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-13 10:07:47,132 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-13 10:07:52,264 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-13 10:09:28,462 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/Aakvatech-Limited/vfd-tz.git
2025-06-13 10:09:28,472 LOG Getting vfd-tz
2025-06-13 10:09:28,472 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/vfd-tz.git  --depth 1 --origin upstream
2025-06-13 10:09:30,606 LOG Installing vfd_tz
2025-06-13 10:09:30,607 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_tz 
2025-06-13 10:09:44,704 DEBUG bench build --app vfd_tz
2025-06-13 10:09:44,846 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app vfd_tz
2025-06-13 10:09:47,033 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-13 10:09:47,245 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-13 10:09:47,245 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-13 10:09:47,924 INFO A newer version of bench is available: 5.25.1 → 5.25.4
2025-06-13 10:10:09,728 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-13 10:10:09,997 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-13 10:10:09,999 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-13 10:10:10,014 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-13 10:10:10,014 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-13 10:10:17,792 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-13 11:30:53,772 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental remove-from-installed-apps shm
2025-06-13 11:31:03,417 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-13 11:36:39,965 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-13 11:36:40,292 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-13 11:36:40,301 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-13 11:36:40,316 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-13 11:36:40,324 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-13 11:37:45,710 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rental set-admin-password aakvatech
2025-06-13 12:00:01,639 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-13 18:00:02,279 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-14 18:00:01,529 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-14 19:43:56,373 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-14 19:44:02,178 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-14 19:44:02,488 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-14 19:44:02,497 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-14 19:44:02,505 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-14 19:44:02,511 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-15 12:00:01,903 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-15 18:00:01,813 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-16 12:00:01,899 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-16 18:00:02,199 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-16 21:07:35,365 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-16 21:07:46,219 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-16 21:07:46,514 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-16 21:07:46,532 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-16 21:07:46,536 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-16 21:07:46,546 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-16 22:25:01,473 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-17 09:20:12,083 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-17 09:20:16,973 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-17 09:20:17,265 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-17 09:20:17,267 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-17 09:20:17,279 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-17 09:20:17,285 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-17 12:00:02,209 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-17 15:00:22,843 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-17 18:00:02,289 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-18 09:22:23,994 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-18 09:22:32,112 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 09:22:32,401 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-18 09:22:32,409 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 09:22:32,412 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 09:22:32,425 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 09:36:04,396 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-18 09:36:12,171 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 09:36:12,440 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 09:36:12,461 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-18 09:36:12,464 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 09:36:12,472 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 10:25:54,278 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-18 10:25:59,137 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 10:25:59,404 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 10:25:59,412 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 10:25:59,414 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-18 10:25:59,419 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 10:30:52,841 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-18 11:40:51,315 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-18 12:00:01,814 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-18 12:07:07,760 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-18 12:09:44,020 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-18 17:56:41,244 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-18 17:56:45,815 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 17:57:27,106 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-18 17:57:31,414 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 17:57:31,663 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-18 17:57:31,695 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 17:57:31,699 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 17:57:31,706 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 17:57:53,035 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 17:57:53,325 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 17:57:53,325 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 17:57:53,325 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 17:57:53,340 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-18 17:58:13,821 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 17:58:14,109 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 17:58:14,115 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 17:58:14,128 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 17:58:14,129 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-18 18:00:02,076 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-18 18:00:23,367 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-18 18:00:23,647 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-18 18:00:23,661 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-18 18:00:23,664 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-18 18:00:23,671 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-19 12:00:01,742 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-19 13:13:21,208 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-19 13:13:25,497 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-19 13:13:25,806 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-19 13:13:25,815 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-19 13:13:25,817 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-19 13:13:25,822 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-19 13:18:54,568 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app csf_tz
2025-06-19 14:38:11,813 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-19 14:38:25,361 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-19 14:38:25,628 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-19 14:38:25,646 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-19 14:38:25,648 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-19 14:38:25,663 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-19 17:03:50,603 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site rubis
2025-06-19 17:05:36,790 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app print_designer
2025-06-19 17:05:37,858 LOG Getting print_designer
2025-06-19 17:05:37,859 DEBUG cd ./apps && git clone https://github.com/frappe/print_designer.git  --depth 1 --origin upstream
2025-06-19 17:05:42,128 LOG Installing print_designer
2025-06-19 17:05:42,129 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/print_designer 
2025-06-19 17:05:47,568 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/print_designer && yarn install --check-files
2025-06-19 17:05:49,970 DEBUG bench build --app print_designer
2025-06-19 17:05:50,142 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app print_designer
2025-06-19 17:06:05,343 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-19 17:06:05,528 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-19 17:06:05,528 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-19 17:06:06,218 INFO A newer version of bench is available: 5.25.1 → 5.25.5
2025-06-19 17:07:05,109 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis restore 20250618_211511-rubis_frappe_cloud-database.sql.gz
2025-06-19 17:09:23,475 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis set-admin-passwords aakvatech
2025-06-19 17:09:33,023 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis set-admin-password aakvatech
2025-06-19 17:09:57,783 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-06-19 17:10:11,165 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-19 17:10:11,542 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-19 17:10:11,544 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-19 17:10:11,559 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-19 17:10:11,564 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-19 17:12:27,918 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-19 17:14:02,421 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis console
2025-06-19 17:15:44,047 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis console
2025-06-19 17:16:56,676 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis execute diagnostic_script.py
2025-06-19 17:17:33,715 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all list-apps
2025-06-19 17:18:09,064 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis console --execute 
import frappe
from frappe.model.delete_doc import check_if_doc_is_linked

print('=== Employee Deletion Diagnostic ===')

# Check if the Employee record exists
try:
    emp = frappe.get_doc('Employee', 'HR-EMP-00025')
    print(f'Employee found: {emp.employee_name}')
except Exception as e:
    print(f'Error getting employee: {e}')
    exit()

# Try the deletion check
try:
    doc = frappe.get_doc('Employee', 'HR-EMP-00025')
    check_if_doc_is_linked(doc)
    print('✓ No linking issues found - Employee can be deleted safely')
except Exception as e:
    print(f'✗ Linking check failed: {e}')
    if 'Unknown column' in str(e) and 'parent' in str(e):
        print('Issue: A table is missing the parent column')
        print('This suggests a child table doctype has incorrect structure')

2025-06-19 17:18:38,369 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis execute frappe.client.delete --args {"doctype": "Employee", "name": "HR-EMP-00025"}
2025-06-19 17:19:06,174 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis mariadb
2025-06-19 17:21:31,605 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench restart
2025-06-19 17:21:42,726 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-06-19 17:21:42,987 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-19 17:21:42,987 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench restart executed with exit code 1
2025-06-19 17:21:43,446 INFO A newer version of bench is available: 5.25.1 → 5.25.5
2025-06-19 17:21:57,645 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis mariadb
2025-06-19 17:35:48,019 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-19 17:37:16,166 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis console
2025-06-19 17:45:33,352 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-19 17:47:41,661 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis console
2025-06-19 17:54:04,491 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-19 18:00:01,580 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-19 18:01:13,749 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis console
2025-06-20 12:00:02,167 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-20 18:00:02,317 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-21 11:58:03,677 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-06-21 11:58:09,261 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-21 11:58:09,645 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-21 11:58:09,645 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-21 11:58:09,649 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-21 11:58:09,652 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-21 12:00:01,708 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-22 18:00:02,165 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-23 00:00:01,997 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-23 12:00:02,035 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-23 12:24:43,927 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-23 12:24:56,950 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-23 12:24:57,295 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-23 12:24:57,297 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-23 12:24:57,311 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-23 12:24:57,316 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-23 13:20:05,520 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-23 18:00:01,607 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-23 18:35:48,764 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-23 18:39:35,988 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-23 18:46:24,631 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-23 18:50:14,936 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-24 00:00:01,329 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-24 12:00:01,823 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-24 17:44:24,038 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-06-24 17:44:30,817 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 17:44:31,089 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 17:44:31,091 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 17:44:31,113 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-24 17:44:31,126 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 17:53:18,056 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-24 17:54:50,155 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use wasco.dev
2025-06-24 17:54:57,238 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 17:54:57,503 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 17:54:57,514 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 17:54:57,524 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-24 17:54:57,555 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 17:56:44,360 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-06-24 17:56:50,695 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 17:56:50,941 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-24 17:56:50,966 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 17:56:50,973 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 17:56:50,987 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 18:00:01,793 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-24 18:04:33,186 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use massumin
2025-06-24 18:04:40,090 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 18:04:40,399 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 18:04:40,434 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-24 18:04:40,447 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 18:04:40,463 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 18:06:14,803 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use mmt.dev
2025-06-24 18:06:19,774 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 18:06:20,141 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 18:06:20,151 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 18:06:20,154 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 18:06:20,163 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-24 18:08:09,837 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-24 18:08:14,791 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 18:08:15,164 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-24 18:08:15,168 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 18:08:15,176 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 18:08:15,207 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 18:09:38,984 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-06-24 18:09:43,316 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-24 18:09:43,665 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-24 18:09:43,675 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-24 18:09:43,684 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-24 18:09:43,690 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-25 09:45:41,017 INFO /home/<USER>/.local/bin/bench use working
2025-06-25 09:46:02,188 INFO /home/<USER>/.local/bin/bench start
2025-06-25 09:46:02,541 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-06-25 09:46:02,542 INFO /home/<USER>/.local/bin/bench worker
2025-06-25 09:46:02,546 INFO /home/<USER>/.local/bin/bench watch
2025-06-25 09:46:02,563 INFO /home/<USER>/.local/bin/bench schedule
2025-06-25 10:46:12,840 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-25 10:48:46,018 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-25 10:48:46,322 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 10:48:46,329 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-25 10:48:46,344 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 10:48:46,373 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 11:17:21,724 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-25 12:00:01,245 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-25 13:03:20,907 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-25 13:03:26,126 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-25 13:03:26,429 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-25 13:03:26,433 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 13:03:26,436 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 13:03:26,469 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 13:14:44,333 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-06-25 13:14:49,586 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-25 13:14:49,880 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 13:14:49,884 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-06-25 13:14:49,905 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 13:14:49,910 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 18:00:02,092 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-26 12:00:02,133 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-26 18:00:01,907 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-27 12:00:01,775 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-28 12:00:02,225 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-28 18:00:01,954 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-29 00:00:02,342 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-29 12:00:02,080 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-29 18:00:01,901 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-30 12:00:02,186 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-30 18:00:02,162 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-01 11:25:16,384 INFO /home/<USER>/.local/bin/bench use working
2025-07-01 11:25:22,305 INFO /home/<USER>/.local/bin/bench start
2025-07-01 11:25:22,645 INFO /home/<USER>/.local/bin/bench watch
2025-07-01 11:25:22,645 INFO /home/<USER>/.local/bin/bench schedule
2025-07-01 11:25:22,662 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-07-01 11:25:22,710 INFO /home/<USER>/.local/bin/bench worker
2025-07-01 12:00:01,992 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-01 18:00:01,429 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-02 12:00:01,247 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-02 16:23:48,478 INFO /home/<USER>/.local/bin/bench use sdg
2025-07-02 16:23:52,613 INFO /home/<USER>/.local/bin/bench start
2025-07-02 16:23:52,975 INFO /home/<USER>/.local/bin/bench schedule
2025-07-02 16:23:52,979 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-07-02 16:23:52,981 INFO /home/<USER>/.local/bin/bench worker
2025-07-02 16:23:52,990 INFO /home/<USER>/.local/bin/bench watch
2025-07-02 16:26:41,524 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-07-02 16:26:47,231 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-02 16:26:47,560 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-02 16:26:47,566 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-02 16:26:47,577 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-02 16:26:47,607 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-02 16:27:11,469 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-02 18:00:01,775 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-03 11:20:54,725 INFO /home/<USER>/.local/bin/bench use rubis
2025-07-03 11:22:06,020 INFO /home/<USER>/.local/bin/bench --site rubis console
2025-07-03 12:00:02,189 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-03 17:12:34,700 INFO /home/<USER>/.local/bin/bench --site massumin restore 20250703_164152-masumin14-av_frappe_cloud-files.tar
2025-07-03 17:13:34,770 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin restore 20250703_164152-masumin14-av_frappe_cloud-files.tar
2025-07-03 17:22:52,440 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin restore 20250703_164152-masumin14-av_frappe_cloud-database.sql.gz
2025-07-03 17:30:14,736 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin set-admin-password aakvatech
2025-07-03 17:30:23,083 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use massumin
2025-07-03 17:31:01,640 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use massumin
2025-07-03 17:31:10,143 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-03 17:31:10,552 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-03 17:31:10,560 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-03 17:31:10,577 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-03 17:31:10,584 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-03 18:00:01,525 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-04 08:27:15,220 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-04 08:27:25,941 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-04 08:27:26,206 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-04 08:27:26,210 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-04 08:27:26,213 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-04 08:27:26,229 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-04 08:28:24,561 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis install-app print_designer
2025-07-04 08:28:36,084 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis install-app print_designer --force
2025-07-04 08:32:07,714 INFO /home/<USER>/.local/bin/bench --site rubis install-app print_designer --force
2025-07-04 08:32:28,989 INFO /home/<USER>/.local/bin/bench --site rubis migrate
2025-07-04 08:33:17,249 INFO /home/<USER>/.local/bin/bench --site rubis install-app print_designer --force
2025-07-04 08:34:46,013 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis migrate
2025-07-04 08:35:00,553 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis install-app print_designer --force
2025-07-04 12:00:02,234 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-04 18:00:02,375 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-05 12:00:02,261 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-05 18:00:01,563 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-05 20:01:00,146 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use working
2025-07-05 20:01:05,298 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-05 20:01:05,611 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-05 20:01:05,625 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-05 20:01:05,629 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-05 20:01:05,645 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-06 00:00:01,960 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-06 10:40:37,364 INFO /home/<USER>/.local/bin/bench use working
2025-07-06 10:41:16,248 INFO /home/<USER>/.local/bin/bench start
2025-07-06 10:41:16,552 INFO /home/<USER>/.local/bin/bench worker
2025-07-06 10:41:16,570 INFO /home/<USER>/.local/bin/bench schedule
2025-07-06 10:41:16,578 INFO /home/<USER>/.local/bin/bench watch
2025-07-06 10:41:16,586 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-07-06 11:42:47,702 INFO /home/<USER>/.local/bin/bench use rental
2025-07-06 11:42:52,122 INFO /home/<USER>/.local/bin/bench start
2025-07-06 11:42:52,424 INFO /home/<USER>/.local/bin/bench worker
2025-07-06 11:42:52,425 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-07-06 11:42:52,426 INFO /home/<USER>/.local/bin/bench schedule
2025-07-06 11:42:52,428 INFO /home/<USER>/.local/bin/bench watch
2025-07-06 11:43:45,891 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-07-06 11:43:50,283 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-06 11:43:50,558 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-06 11:43:50,566 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-06 11:43:50,568 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-06 11:43:50,570 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-06 11:44:10,807 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-06 12:00:01,255 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-06 16:56:35,916 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rental
2025-07-06 16:56:40,400 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-06 16:56:40,685 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-06 16:56:40,685 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-06 16:56:40,691 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-06 16:56:40,697 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-06 17:37:03,700 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-06 17:37:11,404 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-06 17:37:11,686 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-06 17:37:11,690 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-06 17:37:11,703 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-06 17:37:11,715 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-06 17:37:40,634 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-06 17:44:02,985 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-06 17:44:53,888 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-06 17:45:21,456 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-06 17:48:37,485 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-07-06 17:54:57,970 INFO /home/<USER>/.local/bin/bench list-sites
2025-07-06 17:55:08,708 INFO /home/<USER>/.local/bin/bench --site explore show-config
2025-07-06 17:55:15,390 INFO /home/<USER>/.local/bin/bench --site explore doctor
2025-07-06 17:55:34,952 INFO /home/<USER>/.local/bin/bench --site explore console
2025-07-06 17:58:24,386 INFO /home/<USER>/.local/bin/bench --site explore migrate --skip-search-index
2025-07-06 18:00:01,870 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-06 18:00:13,511 INFO /home/<USER>/.local/bin/bench --site explore console
2025-07-06 18:00:51,420 INFO /home/<USER>/.local/bin/bench --site explore execute frappe.utils.has_gravatar --args ["<EMAIL>"]
2025-07-06 18:01:01,544 INFO /home/<USER>/.local/bin/bench --site explore console
2025-07-06 18:01:15,546 INFO /home/<USER>/.local/bin/bench --site explore console
2025-07-06 18:01:22,715 INFO /home/<USER>/.local/bin/bench --site explore migrate
2025-07-06 18:02:04,192 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-06 18:02:29,261 INFO /home/<USER>/.local/bin/bench --site explore migrate --skip-failing
2025-07-06 18:03:46,126 INFO /home/<USER>/.local/bin/bench --site explore mariadb
2025-07-06 18:05:07,492 INFO /home/<USER>/.local/bin/bench --site explore console
2025-07-06 18:05:18,927 INFO /home/<USER>/.local/bin/bench --site explore run-patch --skip-failing
2025-07-06 18:05:34,451 INFO /home/<USER>/.local/bin/bench --site explore console
2025-07-06 18:07:52,940 INFO /home/<USER>/.local/bin/bench --site all migrate --dry-run
2025-07-06 18:08:00,474 INFO /home/<USER>/.local/bin/bench --site all migrate --help
2025-07-06 18:08:08,291 INFO /home/<USER>/.local/bin/bench --site all list-apps
2025-07-06 18:08:28,606 INFO /home/<USER>/.local/bin/bench --site explore migrate --skip-failing
2025-07-06 18:13:22,489 INFO /home/<USER>/.local/bin/bench --site explore list-apps
2025-07-06 18:13:23,955 INFO /home/<USER>/.local/bin/bench --site explore stop
2025-07-06 18:13:36,972 INFO /home/<USER>/.local/bin/bench --site explore execute /tmp/db_optimize.py
2025-07-06 18:13:38,079 INFO /home/<USER>/.local/bin/bench --site explore migrate --skip-failing
2025-07-06 18:13:39,283 INFO /home/<USER>/.local/bin/bench --site explore migrate --dry-run
2025-07-06 18:13:40,066 INFO /home/<USER>/.local/bin/bench --site explore start
2025-07-06 18:13:40,113 WARNING /home/<USER>/.local/bin/bench --site explore start executed with exit code 2
2025-07-06 18:13:40,924 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-06 18:13:41,164 INFO /home/<USER>/.local/bin/bench --site explore build-search-index
2025-07-06 18:15:55,650 INFO /home/<USER>/.local/bin/bench --site explore list-apps
2025-07-06 18:16:07,629 INFO /home/<USER>/.local/bin/bench --site explore migrate --dry-run
2025-07-06 18:16:14,120 INFO /home/<USER>/.local/bin/bench --site explore list-apps
2025-07-06 18:16:27,881 INFO /home/<USER>/.local/bin/bench start
2025-07-06 18:16:28,496 INFO /home/<USER>/.local/bin/bench schedule
2025-07-06 18:16:28,505 INFO /home/<USER>/.local/bin/bench watch
2025-07-06 18:16:28,531 INFO /home/<USER>/.local/bin/bench worker
2025-07-06 18:16:28,674 INFO /home/<USER>/.local/bin/bench serve --port 8003
2025-07-06 18:16:58,969 INFO /home/<USER>/.local/bin/bench migrate
2025-07-07 12:00:01,325 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-07 13:53:54,613 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-07 13:53:58,733 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-07 13:53:59,072 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-07 13:53:59,078 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-07 13:53:59,081 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-07 13:53:59,082 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-07 18:00:01,244 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-07 22:31:42,629 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-07 22:34:36,107 INFO /home/<USER>/.local/bin/bench restart
2025-07-07 22:34:45,682 DEBUG cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe:
2025-07-07 22:34:45,898 WARNING cd /home/<USER>/Desktop/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-07 22:34:45,899 WARNING /home/<USER>/.local/bin/bench restart executed with exit code 1
2025-07-07 22:34:46,890 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-07 22:34:58,422 INFO /home/<USER>/.local/bin/bench start
2025-07-07 22:35:11,282 INFO /home/<USER>/.local/bin/bench console
2025-07-07 22:38:38,678 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-07-07 22:38:49,156 INFO /home/<USER>/.local/bin/bench clear-cache
2025-07-07 22:38:56,883 INFO /home/<USER>/.local/bin/bench console
2025-07-07 22:41:13,069 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-07 22:43:30,771 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-07-07 22:48:42,855 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-07-07 22:48:52,080 INFO /home/<USER>/.local/bin/bench clear-cache
2025-07-07 22:48:59,774 INFO /home/<USER>/.local/bin/bench console
2025-07-07 22:50:36,495 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-07-07 22:51:12,290 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app csf_tz
2025-07-07 22:51:20,713 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 12:00:01,900 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-08 14:57:40,848 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-08 14:58:23,838 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis restore 20250708_145522-rubis_frappe_cloud-database.sql.gz
2025-07-08 15:00:27,993 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis set-admin-passwords aakvatech
2025-07-08 15:00:35,923 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis set-admin-password aakvatech
2025-07-08 15:00:47,757 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-08 15:00:48,025 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-08 15:00:48,026 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-08 15:00:48,031 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-08 15:00:48,032 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-08 15:00:58,436 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 15:17:08,119 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console --execute exec(open('diagnose_hook.py').read())
2025-07-08 15:19:09,674 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --help
2025-07-08 15:19:26,315 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:20:01,767 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:20:33,292 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:20:59,880 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:21:28,936 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:21:55,418 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:22:27,041 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:23:04,868 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:27:36,814 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:28:14,483 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:28:44,897 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:29:17,335 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:30:52,700 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:36:50,657 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-08 15:36:55,496 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-08 15:36:55,765 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-08 15:36:55,776 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-08 15:36:55,783 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-08 15:36:55,788 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-08 15:41:16,112 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench console
2025-07-08 15:49:03,530 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-08 15:49:03,829 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-08 15:49:03,840 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-08 15:49:03,845 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-08 15:49:03,852 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-08 15:56:17,098 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 18:00:01,606 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-08 21:04:04,100 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-08 21:04:09,740 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-08 21:04:10,013 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-08 21:04:10,015 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-08 21:04:10,039 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-08 21:04:10,046 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-08 21:32:12,309 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 21:36:51,386 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 22:11:06,448 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 22:46:56,904 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-08 22:46:57,210 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-08 22:46:57,220 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-08 22:46:57,242 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-08 22:46:57,251 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-08 22:47:15,079 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 23:10:52,960 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 23:14:32,016 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 23:15:13,695 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 23:21:48,621 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 23:28:26,488 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 23:38:34,425 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 08:49:27,263 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-09 08:51:44,438 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis restore 20250708_145522-rubis_frappe_cloud-database.sql.gz
2025-07-09 08:55:12,738 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-09 08:55:13,047 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-09 08:55:13,048 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-09 08:55:13,075 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-09 08:55:13,077 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-09 08:56:01,955 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis set-admin-password aakvatech
2025-07-09 08:56:18,314 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:00:00,113 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:05:59,219 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:11:11,812 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:40:14,503 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:41:23,899 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:43:17,050 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:48:10,656 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:49:20,825 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:50:20,494 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:56:25,006 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all console
2025-07-09 09:57:40,303 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:01:44,241 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:11:00,021 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:11:53,727 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:12:48,621 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:13:30,913 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:14:47,149 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:17:18,356 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:18:07,989 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:22:25,304 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 10:23:14,929 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 11:20:51,090 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all console
2025-07-09 12:00:01,390 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-09 13:56:39,846 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore /home/<USER>/Downloads/20250709_133659-silverentertrade14-sf-av_frappe_cloud-database.sql.gz
2025-07-09 13:57:16,488 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore 20250709_133659-silverentertrade14-sf-av_frappe_cloud-database.sql.gz
2025-07-09 13:57:37,968 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore restore 20250709_133659-silverentertrade14-sf-av_frappe_cloud-database.sql.gz
2025-07-09 14:06:54,340 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-09 14:07:02,171 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-09 14:07:02,588 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-09 14:07:02,589 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-09 14:07:02,598 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-09 14:07:02,618 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-09 14:07:29,689 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore set-admin-password aakvatech
2025-07-09 14:07:41,883 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 14:28:44,817 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore mariadb
2025-07-09 14:35:59,218 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore mariadb
2025-07-09 14:39:50,490 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore mariadb
2025-07-09 14:49:22,835 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore mariadb
2025-07-09 14:52:11,213 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore mariadb
2025-07-09 15:54:16,590 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site explore mariadb
2025-07-09 16:42:53,726 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all execute csf_tz.patches.remove_invalid_student_field_from_sales_order.execute
2025-07-09 16:43:02,325 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site-config
2025-07-09 16:43:15,007 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site massumin console
2025-07-09 18:00:01,850 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-10 10:02:46,773 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site clouds
2025-07-10 10:04:03,629 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site clouds restore 20250710_065926-cmgroup-av_frappe_cloud-files.tar
2025-07-10 12:00:01,343 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-10 14:34:44,898 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-10 14:34:53,508 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-10 14:34:53,850 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-10 14:34:53,854 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-10 14:34:53,882 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-10 14:34:53,898 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-10 14:55:08,847 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use sdg
2025-07-10 14:55:14,388 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-10 14:55:14,728 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-10 14:55:14,737 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-10 14:55:14,749 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-10 14:55:14,765 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-10 16:15:41,494 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-10 16:15:47,635 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-10 16:15:47,983 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-10 16:15:47,996 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-10 16:15:48,006 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-10 16:15:48,008 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-10 17:06:01,657 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use explore
2025-07-10 17:06:10,758 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-10 17:06:11,169 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-10 17:06:11,176 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-10 17:06:11,204 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-10 17:06:11,216 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-10 18:00:02,176 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-11 11:14:07,201 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-11 11:14:12,970 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-11 11:14:13,287 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-11 11:14:13,289 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-11 11:14:13,298 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-11 11:14:13,302 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-11 11:33:37,336 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-11 11:39:01,330 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-11 12:00:01,776 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-11 15:56:28,067 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-11 16:00:44,223 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-11 16:04:48,963 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-11 18:00:01,919 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-12 12:00:02,181 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-12 18:00:01,848 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-07-12 22:25:18,631 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use rubis
2025-07-12 22:25:57,984 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-12 22:25:58,294 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-12 22:25:58,300 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-12 22:25:58,309 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8003
2025-07-12 22:25:58,311 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-12 22:35:09,692 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all console
2025-07-12 22:42:03,367 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-12 22:45:54,863 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis clear-cache
2025-07-12 22:46:05,275 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis clearwebsite--cache
2025-07-12 22:46:19,286 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site rubis clear-website-cache
2025-07-12 22:46:50,736 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-12 22:46:56,299 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
