2025-04-09 10:33:35,156 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 11:46:13,581 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'NHIMA Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 11:53:41,824 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'NHIMA Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 12:40:40,055 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'NHIMA Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-15 13:23:30,832 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-exfnuzeflk","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zcpofolqkt","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-exfnuzeflk","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"this cargo","value":75000,"weight":8000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"Kiboko ltd","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","customer_address":"Kiboko ltd-Billing","shipper":"John Doe","airline":"","departure_date":"2025-04-18","arrival_date":"2025-04-29","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 13:23:55,732 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-exfnuzeflk","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zcpofolqkt","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-exfnuzeflk","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"this cargo","value":75000,"weight":8000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"Kiboko ltd","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","customer_address":"Kiboko ltd-Billing","shipper":"John Doe","airline":"","departure_date":"2025-04-18","arrival_date":"2025-04-29","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 13:24:10,170 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-exfnuzeflk","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zcpofolqkt","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-exfnuzeflk","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"this cargo","value":75000,"weight":8000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"Kiboko ltd","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","customer_address":"Kiboko ltd-Billing","shipper":"John Doe","airline":"","departure_date":"2025-04-18","arrival_date":"2025-04-29","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 14:08:03,634 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-iapppxlnkb","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-qjvnrtfdii","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-iapppxlnkb","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"cargo B","value":45000,"weight":35000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"kibo","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\n23116<br>Tanzania<br>\\n<br>\\n","customer_address":"kibo-Billing","shipper":"John Doe","departure_date":"2025-04-18","arrival_date":"2025-04-30","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 14:11:31,370 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-pujhyairie","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zozivubmhd","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-pujhyairie","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"cargo b","cargo_description":"cargo b","value":500000,"weight":35000}],"document":[],"declaration_type":"","cl_plan":"","customer":"kibo","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\n23116<br>Tanzania<br>\\n<br>\\n","customer_address":"kibo-Billing","departure_date":"2025-04-18","arrival_date":"2025-04-30","shipper":"John Doe","mode_of_transport":"Air","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-18 10:58:50,399 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'chart_name': 'Hiring vs Attrition Count', 'filters': '{"time_interval":"Monthly","company":"Design System"}', 'refresh': '1', 'time_interval': '', 'timespan': '', 'from_date': '', 'to_date': '', 'heatmap_year': '', 'cmd': 'hrms.hr.dashboard_chart_source.hiring_vs_attrition_count.hiring_vs_attrition_count.get_data'}
2025-04-19 18:42:59,756 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Accumulated Depreciation - DS","party_type":"","account_type":"Accumulated Depreciation","exchange_rate":1,"party":"","debit":0,"credit":250000,"debit_in_account_currency":null,"credit_in_account_currency":250000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":0,"total_credit":250000,"difference":-250000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:43:23,283 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Capital Equipments - DS","party_type":"","account_type":"Fixed Asset","exchange_rate":1,"party":"","debit":0,"credit":250000,"debit_in_account_currency":null,"credit_in_account_currency":250000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":0,"total_credit":250000,"difference":-250000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:44:55,361 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Expenses Included In Asset Valuation - DS","party_type":"","account_type":"Expenses Included In Asset Valuation","exchange_rate":1,"party":"","debit":150000,"credit":0,"debit_in_account_currency":150000,"credit_in_account_currency":0}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":150000,"total_credit":0,"difference":150000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:52:41,487 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"laptop depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":150000,"credit":0,"debit_in_account_currency":150000,"credit_in_account_currency":0}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":150000,"total_credit":0,"difference":150000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:54:50,235 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"laptop depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":150000,"credit":0,"debit_in_account_currency":150000,"credit_in_account_currency":0}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":150000,"total_credit":0,"difference":150000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:54:57,833 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'journal-entry/new-journal-entry-rkyyezrpho'}
2025-04-19 19:00:38,865 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-mgwybdfrvs","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-skvcpaausx","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-mgwybdfrvs","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Accumulated Depreciation - DS","party_type":"","account_type":"Accumulated Depreciation","exchange_rate":1,"party":"","debit":50000,"credit":0,"debit_in_account_currency":50000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-02","total_debit":50000,"total_credit":0,"difference":50000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 19:00:55,758 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-mgwybdfrvs","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-skvcpaausx","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-mgwybdfrvs","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Accumulated Depreciation - DS","party_type":"","account_type":"Accumulated Depreciation","exchange_rate":1,"party":"","debit":50000,"credit":0,"debit_in_account_currency":50000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-02","total_debit":50000,"total_credit":0,"difference":50000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 19:06:05,187 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-bwgqwwuukn","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-pcxbhvgupi","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-bwgqwwuukn","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Laptop Depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":550000,"credit":0,"debit_in_account_currency":550000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-19","total_debit":550000,"total_credit":0,"difference":550000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 19:33:02,723 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-bwgqwwuukn","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-pcxbhvgupi","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-bwgqwwuukn","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Laptop Depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":550000,"credit":0,"debit_in_account_currency":550000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-19","total_debit":550000,"total_credit":0,"difference":550000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 14:07:11,440 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CF-DN-2025-00282","owner":"<EMAIL>","creation":"2025-04-22 12:32:41.358742","modified":"2025-04-22 14:07:07.336379","modified_by":"<EMAIL>","docstatus":0,"idx":0,"clearing_file":"CF-2025-0204","consignee":"Kiboko ltd","posting_date":"2025-04-22","address":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","exporter_type":"In-house","has_container_interchange":0,"doctype":"CF Delivery Note","truck":[],"delivery_date":"2025-04-23","loading_date":"2025-04-23"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-24 09:23:43,674 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Exited Containers', 'filters': '{"from_date":"2025-03-24","to_date":"2025-04-24"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-04-24 09:23:43,790 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Exited Containers', 'filters': '{"from_date":"2025-03-24","to_date":"2025-04-24"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-24 09:31:17,366 ERROR frappe Error while inserting deferred Error Log record: Error Log 2olo2dfmlb: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '.received_date,\n            c.arrival_date,\n            gp.submitted_date,\n  ...' at line 5")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-04-24 09:31:17,369 ERROR frappe Error while inserting deferred Error Log record: Error Log 2olej2c7ev: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '.received_date,\n            c.arrival_date,\n            gp.submitted_date,\n  ...' at line 5")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-04-24 15:58:56,712 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'app_path': 'clearing-&-forwarding'}
2025-04-26 16:00:44,852 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Exited Containers', 'filters': '{"from_date":"2025-03-26","to_date":"2025-04-26"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-04-26 16:00:44,979 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Exited Containers', 'filters': '{"from_date":"2025-03-26","to_date":"2025-04-26"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-26 16:00:48,075 ERROR frappe Error while inserting deferred Error Log record: Error Log ug0b4lnj16: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AND c.arrival_date >= '2025-03-26' AND c.arrival_date <= '2025-04-26'\n       ...' at line 17")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-04-26 16:00:48,077 ERROR frappe Error while inserting deferred Error Log record: Error Log ug0cbcvqbv: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AND c.arrival_date >= '2025-03-26' AND c.arrival_date <= '2025-04-26'\n       ...' at line 17")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-04-26 18:51:32,358 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'data': '{"m_bl_no":"DXB06001"}', 'cmd': 'icd_tz.icd_tz.api.sales_order.create_sales_order'}
2025-04-26 18:58:27,610 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'data': '{"h_bl_no":" DXB06001"}', 'cmd': 'icd_tz.icd_tz.api.sales_order.create_sales_order'}
2025-04-26 18:58:30,313 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'data': '{"h_bl_no":" DXB06001"}', 'cmd': 'icd_tz.icd_tz.api.sales_order.create_sales_order'}
2025-04-26 18:58:38,129 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'data': '{"h_bl_no":" DXB06001"}', 'cmd': 'icd_tz.icd_tz.api.sales_order.create_sales_order'}
2025-04-28 08:55:11,374 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-28 17:06:21,732 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCF Employee', 'filters': '{"from_date":"2025-01-01","to_date":"2025-04-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-04-28 21:30:35,179 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Exited Containers', 'filters': '{"from_date":"2025-03-28","to_date":"2025-04-29"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-04-28 21:30:35,263 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Exited Containers', 'filters': '{"from_date":"2025-03-28","to_date":"2025-04-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-04-28 21:45:24,591 ERROR frappe Error while inserting deferred Error Log record: Error Log vtdafkv6n7: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '.received_date,\n            c.arrival_date,\n            gp.submitted_date,\n  ...' at line 5")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-04-28 21:45:24,593 ERROR frappe Error while inserting deferred Error Log record: Error Log vtdooett60: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '.received_date,\n            c.arrival_date,\n            gp.submitted_date,\n  ...' at line 5")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-04-28 22:05:06,635 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'docs': '{"docstatus":0,"doctype":"Manifest","name":"new-manifest-dmcyxzqcpz","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"ICD Tanzania Limited","port":"TEAGTL","naming_series":"ICD-M-.YYYY.-","containers":[{"docstatus":0,"doctype":"Containers Detail","name":"new-containers-detail-xpgbrkfkeb","__islocal":1,"__unsaved":1,"owner":"Administrator","has_order":0,"parent":"new-manifest-dmcyxzqcpz","parentfield":"containers","parenttype":"Manifest","idx":1,"__unedited":false,"m_bl_no":"DXB0933918","container_no":"TXGU7294610","container_size":"45G1","no_of_packages":"98"}],"hbl_containers":[{"docstatus":0,"doctype":"HBL Container","name":"new-hbl-container-xdcoovmhyf","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-manifest-dmcyxzqcpz","parentfield":"hbl_containers","parenttype":"Manifest","idx":1,"__unedited":false,"h_bl_no":"100925000636","m_bl_no":"DXB0933918","container_no":"TXGU7294610","container_size":"45G1","no_of_packages":"98"}],"master_bl":[],"house_bl":[],"arrival_date":"2025-04-29","tpa_uid":"7654","voyage_no":"12345","call_sign":"done","vessel_name":"YOLANDA","mrn":"3456","manifest":"/files/Screenshot from 2025-04-28 16-54-05.png"}', 'method': 'extract_data_from_manifest_file', 'args': '{}', 'cmd': 'run_doc_method'}
2025-04-28 22:05:39,489 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'docs': '{"name":"ICD-M-2025-00001","owner":"Administrator","creation":"2025-04-28 22:05:06.676822","modified":"2025-04-28 22:05:06.676822","modified_by":"Administrator","docstatus":0,"idx":0,"manifest":"/files/29752 employees at WCF.xlsx","company":"ICD Tanzania Limited","mrn":"3456","vessel_name":"YOLANDA","call_sign":"done","voyage_no":"12345","tpa_uid":"7654","arrival_date":"2025-04-29","port":"TEAGTL","amended_from":null,"naming_series":"ICD-M-.YYYY.-","doctype":"Manifest","master_bl":[],"hbl_containers":[{"name":"beqc22ql8p","owner":"Administrator","creation":"2025-04-28 22:05:06.676822","modified":"2025-04-28 22:05:06.676822","modified_by":"Administrator","docstatus":0,"idx":1,"m_bl_no":"DXB0933918","h_bl_no":"100925000636","type_of_container":null,"container_no":"TXGU7294610","container_size":"45G1","seal_no1":null,"seal_no2":null,"seal_no3":null,"freight_indicator":null,"no_of_packages":"98","package_unit":null,"volume":null,"volume_unit":null,"weight":null,"weight_unit":null,"plug_type_of_reefer":null,"minimum_temperature":null,"maximum_temperature":null,"parent":"ICD-M-2025-00001","parentfield":"hbl_containers","parenttype":"Manifest","doctype":"HBL Container","__unsaved":1}],"house_bl":[],"containers":[{"name":"beqkbe7s9l","owner":"Administrator","creation":"2025-04-28 22:05:06.676822","modified":"2025-04-28 22:05:06.676822","modified_by":"Administrator","docstatus":0,"idx":1,"m_bl_no":"DXB0933918","type_of_container":null,"container_no":"TXGU7294610","container_size":"45G1","seal_no1":null,"seal_no2":null,"seal_no3":null,"freight_indicator":null,"no_of_packages":"98","package_unit":null,"volume":null,"volume_unit":null,"weight":null,"weight_unit":null,"plug_type_of_reefer":null,"minimum_temperature":null,"maximum_temperature":null,"has_order":0,"parent":"ICD-M-2025-00001","parentfield":"containers","parenttype":"Manifest","doctype":"Containers Detail","__unsaved":1}],"__last_sync_on":"2025-04-28T19:05:06.726Z","__unsaved":1}', 'method': 'extract_data_from_manifest_file', 'args': '{}', 'cmd': 'run_doc_method'}
2025-04-28 22:06:48,737 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'docs': '{"docstatus":0,"doctype":"Manifest","name":"new-manifest-bpmeyhkgzx","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"ICD Tanzania Limited","port":"TEAGTL","naming_series":"ICD-M-.YYYY.-","containers":[],"hbl_containers":[],"master_bl":[],"house_bl":[],"mrn":"4567","vessel_name":"YOLANDA","call_sign":"done","voyage_no":"7654","tpa_uid":"345","arrival_date":"2025-05-01","manifest":"/private/files/Container_Booking_Datewise_Report (5).xls"}', 'method': 'extract_data_from_manifest_file', 'args': '{}', 'cmd': 'run_doc_method'}
2025-05-08 13:06:47,996 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'is_private': 'NaN', 'folder': 'Home', 'library_file_name': 'b7748f17b7', 'doctype': 'Manifest', 'docname': 'new-manifest-qtpvxpyokn', 'fieldname': 'manifest', 'cmd': 'upload_file'}
2025-05-17 14:08:09,886 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'recipients': '<EMAIL>, ', 'subject': 'Client Script: stock entry script', 'content': '<div class="ql-editor read-mode"><p>that is client script</p></div>', 'doctype': 'Client Script', 'name': 'stock entry script', 'send_email': '1', 'print_html': '', 'send_me_a_copy': '0', 'print_format': 'Standard', 'attachments': '[]', 'read_receipt': '0', 'print_letterhead': '1', 'send_after': '', 'print_language': 'en', 'cmd': 'frappe.core.doctype.communication.email.make'}
2025-05-20 10:53:06,301 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'doc': '{"name":"ICD-C-2025-01662","owner":"<EMAIL>","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":7,"m_bl_no":"************","container_no":"EGHU8397896","type_of_container":"C","no_of_packages":"1","seal_no_1":"EMCRXF3433","seal_no_2":"","seal_no_3":"","freight_indicator":"FCL","volume":0,"weight":"8300","weight_unit":"KG","size":"45GP","cargo_type":"Local","container_count":"15/15","is_empty_container":0,"has_hbl":0,"consignee":"TO THE ORDER OF CRDB BANK PLC","sline":"GULF BADR (L)","sline_code":"GBG","ship":"MV KMTC HOCHIMINH","voyage_no":"2502W","port_of_loading":"CNSHA","port_of_destination":"TEAGTL","place_of_delivery":"WITZDL019","abbr_for_destination":"TZDAR","place_of_destination":"Local","country_of_destination":"Tanzania","gross_volume":900,"gross_volume_unit":"CBM","gross_weight":124500,"gross_weight_unit":"KG","net_weight":124500,"net_weight_unit":"KG","package_unit":"PK","cargo_description":"3 AXLES TIPPER SEMI TRAILERS U SHAPED 15 UNITS OF 3 AXLES TIPPER SEMI TRAILERS U SHAPED CHASSIS NO.: LJRD09378S2004425   LJRD0937XS2004426  LJRD09370S2004435 LJRD09372S2004436   LJRD09374S2004437   LJRD09376S2004438 LJRD09378S2004439   LJRD09371S2004427   LJRD09373S2004428 LJRD09375S2004429   LJRD09371S2004430   LJRD09373S2004431 LJRD09375S2004432   LJRD09377S2004433   LJRD09379S2004434","arrival_date":"2025-04-21","received_date":"2025-04-29","booking_date":"2025-05-02","posting_date":"2025-05-02","last_inspection_date":"2025-05-02","original_location":"B1","current_location":"B1","status":"At Gatepass","naming_series":"ICD-C-.YYYY.-","manifest":"ICD-M-2025-00035","movement_order":"ICD-CMO-2025-01667","container_reception":"ICD-CR-2025-01642","company":"Silver Entertrade Limited","total_days":21,"no_of_free_days":7,"no_of_writeoff_days":0,"no_of_billable_days":14,"days_to_be_billed":14,"no_of_billed_days":0,"has_removal_charges":"Yes","has_corridor_levy_charges":"No","c_sales_invoice":"ACC-SINV-2025-03056","has_single_charge":1,"has_double_charge":1,"doctype":"Container","container_dates":[{"name":"8jncnumo04","owner":"<EMAIL>","creation":"2025-05-02 20:59:00.453782","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":1,"date":"2025-04-29","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6jtm4el9sr","owner":"<EMAIL>","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":2,"date":"2025-04-30","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6jtm7mj6fc","owner":"<EMAIL>","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":3,"date":"2025-05-01","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6jtm5rmk7t","owner":"<EMAIL>","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":4,"date":"2025-05-02","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"bv2p102488","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":5,"date":"2025-05-03","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"eargsb7lqq","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":6,"date":"2025-05-04","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"4mekiv6bbc","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":7,"date":"2025-05-05","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"82ah44gd9s","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":8,"date":"2025-05-06","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"cdgj5qap9f","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":9,"date":"2025-05-07","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"fplvaudfr8","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":10,"date":"2025-05-08","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"b5ds6lferv","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":11,"date":"2025-05-09","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"fhcru0cv0i","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":12,"date":"2025-05-10","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"2srefjupa0","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":13,"date":"2025-05-11","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"f92hv2pq94","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":14,"date":"2025-05-12","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"5kfdf20pbb","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":15,"date":"2025-05-13","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"f08nn015pi","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":16,"date":"2025-05-14","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"fc77rpjf1t","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":17,"date":"2025-05-15","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"8nt0drj3hb","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":18,"date":"2025-05-16","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"73j1vvtm82","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":19,"date":"2025-05-17","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6faa76pobj","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":20,"date":"2025-05-18","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"5r2lmp6le6","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":21,"date":"2025-05-19","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"}],"__last_sync_on":"2025-05-20T07:52:58.999Z","plug_type_of_reefer":"Y","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-05-20 10:53:35,603 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'doc': '{"name":"ICD-C-2025-01662","owner":"<EMAIL>","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":7,"m_bl_no":"************","container_no":"EGHU8397896","type_of_container":"C","no_of_packages":"1","seal_no_1":"EMCRXF3433","seal_no_2":"","seal_no_3":"","freight_indicator":"FCL","volume":0,"weight":"8300","weight_unit":"KG","size":"45GP","cargo_type":"Local","container_count":"15/15","is_empty_container":0,"has_hbl":0,"consignee":"TO THE ORDER OF CRDB BANK PLC","sline":"GULF BADR (L)","sline_code":"GBG","ship":"MV KMTC HOCHIMINH","voyage_no":"2502W","port_of_loading":"CNSHA","port_of_destination":"TEAGTL","place_of_delivery":"WITZDL019","abbr_for_destination":"TZDAR","place_of_destination":"Local","country_of_destination":"Tanzania","gross_volume":900,"gross_volume_unit":"CBM","gross_weight":124500,"gross_weight_unit":"KG","net_weight":124500,"net_weight_unit":"KG","package_unit":"PK","cargo_description":"3 AXLES TIPPER SEMI TRAILERS U SHAPED 15 UNITS OF 3 AXLES TIPPER SEMI TRAILERS U SHAPED CHASSIS NO.: LJRD09378S2004425   LJRD0937XS2004426  LJRD09370S2004435 LJRD09372S2004436   LJRD09374S2004437   LJRD09376S2004438 LJRD09378S2004439   LJRD09371S2004427   LJRD09373S2004428 LJRD09375S2004429   LJRD09371S2004430   LJRD09373S2004431 LJRD09375S2004432   LJRD09377S2004433   LJRD09379S2004434","arrival_date":"2025-04-21","received_date":"2025-04-29","booking_date":"2025-05-02","posting_date":"2025-05-02","last_inspection_date":"2025-05-02","original_location":"B1","current_location":"B1","status":"At Gatepass","naming_series":"ICD-C-.YYYY.-","manifest":"ICD-M-2025-00035","movement_order":"ICD-CMO-2025-01667","container_reception":"ICD-CR-2025-01642","company":"Silver Entertrade Limited","total_days":21,"no_of_free_days":7,"no_of_writeoff_days":0,"no_of_billable_days":14,"days_to_be_billed":14,"no_of_billed_days":0,"has_removal_charges":"Yes","has_corridor_levy_charges":"No","c_sales_invoice":"ACC-SINV-2025-03056","has_single_charge":1,"has_double_charge":1,"doctype":"Container","container_dates":[{"name":"8jncnumo04","owner":"<EMAIL>","creation":"2025-05-02 20:59:00.453782","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":1,"date":"2025-04-29","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6jtm4el9sr","owner":"<EMAIL>","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":2,"date":"2025-04-30","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6jtm7mj6fc","owner":"<EMAIL>","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":3,"date":"2025-05-01","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6jtm5rmk7t","owner":"<EMAIL>","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":4,"date":"2025-05-02","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"bv2p102488","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":5,"date":"2025-05-03","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"eargsb7lqq","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":6,"date":"2025-05-04","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"4mekiv6bbc","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":7,"date":"2025-05-05","is_billable":0,"is_free":1,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"82ah44gd9s","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":8,"date":"2025-05-06","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"cdgj5qap9f","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":9,"date":"2025-05-07","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"fplvaudfr8","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":10,"date":"2025-05-08","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"b5ds6lferv","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":11,"date":"2025-05-09","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"fhcru0cv0i","owner":"Administrator","creation":"2025-04-24 12:28:43.636479","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":12,"date":"2025-05-10","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"2srefjupa0","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":13,"date":"2025-05-11","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"f92hv2pq94","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":14,"date":"2025-05-12","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"5kfdf20pbb","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":15,"date":"2025-05-13","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"f08nn015pi","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":16,"date":"2025-05-14","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"fc77rpjf1t","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":17,"date":"2025-05-15","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"8nt0drj3hb","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":18,"date":"2025-05-16","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"73j1vvtm82","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":19,"date":"2025-05-17","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"6faa76pobj","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":20,"date":"2025-05-18","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"},{"name":"5r2lmp6le6","owner":"Administrator","creation":"2025-05-02 20:59:00.993036","modified":"2025-05-19 18:01:14.620230","modified_by":"Administrator","docstatus":0,"idx":21,"date":"2025-05-19","is_billable":1,"is_free":0,"parent":"ICD-C-2025-01662","parentfield":"container_dates","parenttype":"Container","doctype":"Container Service Detail"}],"__last_sync_on":"2025-05-20T07:52:58.999Z","plug_type_of_reefer":"N","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-05-23 09:02:30,222 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:02:30,464 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:02:30,839 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:02:38,443 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:02:38,757 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:31:58,463 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:31:58,761 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:03,104 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:03,387 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:10,576 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:10,865 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:13,210 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:13,505 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:14,707 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:14,993 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:59,247 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-23 09:32:59,770 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-05-26 08:59:38,195 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-31 14:58:00,824 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'company': 'Shree Hindu Mandal Hospital - Dar es Salaam', 'cmd': 'hms_tz.nhif.report.itemwise_hospital_revenue.itemwise_hospital_revenue.get_payment_modes'}
2025-05-31 15:00:09,839 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'company': 'Shree Hindu Mandal Hospital - Dar es Salaam', 'cmd': 'hms_tz.nhif.report.itemwise_hospital_revenue.itemwise_hospital_revenue.get_payment_modes'}
2025-05-31 15:02:57,845 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'company': 'Shree Hindu Mandal Hospital - Dar es Salaam', 'cmd': 'hms_tz.nhif.report.itemwise_hospital_revenue.itemwise_hospital_revenue.get_payment_modes'}
2025-05-31 15:13:06,399 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'company': 'Shree Hindu Mandal Hospital - Dar es Salaam', 'cmd': 'hms_tz.nhif.report.itemwise_hospital_revenue.itemwise_hospital_revenue.get_payment_modes'}
2025-06-02 09:33:56,422 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:33:58,474 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:18,022 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:18,947 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:19,141 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:19,314 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:19,571 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:19,703 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:35:19,890 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 09:48:42,293 ERROR frappe Failed to capture exception
Site: rental
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-02 09:50:53,900 ERROR frappe Failed to capture exception
Site: rental
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-02 10:06:40,897 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2024-01-01","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:09:37,474 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:12:16,544 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:13:01,024 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:16:08,304 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:17:47,753 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:22:02,723 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:24:08,869 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:24:10,456 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:24:25,299 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:26:31,149 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 10:39:42,091 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 10:44:46,409 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 10:44:47,571 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-06-02 10:58:23,795 ERROR frappe New Exception collected in error log
Site: massumin
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Masumin Printways and Stationers Limited","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-02 11:06:12,772 ERROR frappe Failed to capture exception
Site: rental
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-02 11:17:23,732 ERROR frappe Failed to capture exception
Site: rental
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-02 11:25:12,854 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemwise Hospital Revenue', 'filters': '{"company":"Shree Hindu Mandal - Dar es Salaam","from_date":"2023-01-02","to_date":"2025-06-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:24:56,845 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00005","patient_appointment":"HLC-APP-2024-00353"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:25:09,575 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00008","patient_appointment":"HLC-APP-2024-00353"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:25:15,827 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00008","patient_appointment":"HLC-APP-2024-00353","patient_type":"In-Patient"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:28:21,858 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00005","patient_appointment":"HLC-APP-2024-00353"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:28:27,359 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00005","patient_appointment":"HLC-APP-2024-00353"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:28:28,467 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00005","patient_appointment":"HLC-APP-2024-00353"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 10:30:08,421 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'report_name': 'Itemized Bill Report', 'filters': '{"patient":"N1-0824-00008","patient_appointment":"HLC-APP-2024-00353"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-06-03 17:55:09,239 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-03 17:55:09,271 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-04 11:28:54,910 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'name': 'HLC-APP-2025-00001', 'cmd': 'hms_tz.nhif.api.patient_appointment.invoice_appointment'}
2025-06-04 11:29:56,498 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'doc': '{"name":"HLC-VTS-2025-00001","owner":"<EMAIL>","creation":"2025-06-04 11:29:53.184448","modified":"2025-06-04 11:29:53.184448","modified_by":"<EMAIL>","docstatus":0,"idx":0,"naming_series":"HLC-VTS-.YYYY.-","title":"REUBEN F MCHAURU on 04-06-2025","patient":"N1-0824-00002","patient_name":"REUBEN F MCHAURU","image":null,"inpatient_record":null,"appointment":"HLC-APP-2025-00001","mode_of_payment":"Cash Janet","practitioner":"DR ABDALLAH HOZA","medical_department":"General","healthcare_practitioner":"Innocent Imetumba","encounter":null,"company":"Nephro One Dialysis Clinic","shm_coverage_plan_name":"","signs_date":"2025-06-04","signs_time":"11:29:41","patient_progress":"To Doctor","temperature":null,"pulse":null,"respiratory_rate":null,"oxygen_saturation_spo2":null,"rbg":0,"tongue":"","abdomen":"","reflexes":"","bp_systolic":null,"bp_diastolic":null,"bp":null,"vital_signs_note":null,"visual_acuity_re":null,"visual_acuity_le":null,"intraocular_pressure_re":null,"intraocular_pressure_le":null,"eye_opening":"","verbal_response":"","motor_response":"","height":0,"weight":0,"height_in_cm":0,"bmi":0,"nutrition_note":null,"amended_from":null,"doctype":"Vital Signs","__last_sync_on":"2025-06-04T08:29:53.331Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-14 19:45:23,781 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-06-19 17:12:04,289 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-06-19 17:14:46,578 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-06-19 17:19:12,437 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-06-19 17:37:09,315 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00026', 'cmd': 'frappe.client.delete'}
2025-06-19 17:37:52,519 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00026', 'cmd': 'frappe.client.delete'}
2025-06-23 18:37:47,606 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CC-CF-2025-0438-00450","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":0,"clearing_file":"CF-2025-0438","status":"Paid","consigee":"Customer 19","tra_clearance_total":30000,"port_clearance_total":30000,"shipment_clearance_total":0,"physical_clearance_total":45677,"total_charges_sum":105677,"invoice_number":"ACC-SINV-2025-00038","debit_note_number":"ACC-SINV-2025-00036","doctype":"Clearing Charges","charges":[{"name":"2j8jodevbn","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"Port Clearance","currency":"TZS","amount":30000,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"},{"name":"e92f9f1r74","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":2,"charge_type":"Shipment Clearance","currency":"TZS","amount":39000,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"},{"name":"l4213mio47","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":3,"charge_type":"Physical Verification","currency":"TZS","amount":45677,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"},{"name":"uuitlp1eh0","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":4,"charge_type":"TRA Clearance","currency":"TZS","amount":30000,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"}],"service_charges":[{"docstatus":0,"doctype":"Clearing Service Charge Detail","name":"new-clearing-service-charge-detail-wjwemuqwac","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"CC-CF-2025-0438-00450","parentfield":"service_charges","parenttype":"Clearing Charges","idx":1,"__unedited":true}],"__last_sync_on":"2025-06-23T15:37:31.646Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-23 18:46:33,472 ERROR frappe Error while inserting deferred Error Log record: Error Log 93uqqjedho: 'Title' (Module import failed for Clearing Service Charge Detail, the DocType you're trying to open might be deleted.
Error: No module named 'clearing.clearing.doctype.clearing_service_charge_detail.clearing_service_charge_detail') will get truncated, as max characters allowed is 140
Site: working
Form Dict: {}
2025-06-25 11:15:54,065 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'sales-invoice/ACC-SINV-2025-00045'}
2025-06-25 11:15:54,068 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'sales-invoice/ACC-SINV-2025-00045'}
2025-06-25 11:15:55,724 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'sales-invoice/ACC-SINV-2025-00045'}
2025-07-02 16:24:02,779 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-07-02 16:25:50,111 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-07-06 11:43:11,629 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {}
2025-07-06 17:37:25,843 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-07-06 17:37:28,512 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-07-08 21:20:53,437 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"docstatus":0,"doctype":"Travel Request","name":"new-travel-request-jaugmtsivu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","travel_type":"Domestic","travel_funding":"Require Full Funding","company":"Rubis Technical Services Limited","itinerary":[{"docstatus":0,"doctype":"Travel Itinerary","name":"new-travel-itinerary-edxvxoytit","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","mode_of_travel":"Bus","meal_preference":"","travel_advance_required":0,"lodging_required":0,"parent":"new-travel-request-jaugmtsivu","parentfield":"itinerary","parenttype":"Travel Request","idx":1,"__unedited":false,"travel_from":"Dar","travel_to":"Arusha","departure_date":"2025-07-08 21:20:13"}],"costings":[{"docstatus":0,"doctype":"Travel Request Costing","name":"new-travel-request-costing-slyjuzbqix","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-travel-request-jaugmtsivu","parentfield":"costings","parenttype":"Travel Request","idx":1,"__unedited":false,"expense_type":"Accommodation","custom_no_of_days":4,"total_amount":120000,"custom_cost_per_day":30000}],"total_travel_cost":120000,"purpose_of_travel":"Site Survey","employee_name":"Daudi Thomas Majinge","cell_number":"0685375814","prefered_email":"<EMAIL>","custom_department":"Technical - A","date_of_birth":"1996-07-03","passport_number":"NA","custom_hod":"<EMAIL>","employee":"HR-EMP-00006","cost_center":"Main - A"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-08 21:21:15,653 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"docstatus":0,"doctype":"Travel Request","name":"new-travel-request-jaugmtsivu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","travel_type":"Domestic","travel_funding":"Require Full Funding","company":"Rubis Technical Services Limited","itinerary":[{"docstatus":0,"doctype":"Travel Itinerary","name":"new-travel-itinerary-edxvxoytit","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","mode_of_travel":"Bus","meal_preference":"","travel_advance_required":0,"lodging_required":0,"parent":"new-travel-request-jaugmtsivu","parentfield":"itinerary","parenttype":"Travel Request","idx":1,"__unedited":false,"travel_from":"Dar","travel_to":"Arusha","departure_date":"2025-07-08 21:20:13"}],"costings":[{"docstatus":0,"doctype":"Travel Request Costing","name":"new-travel-request-costing-slyjuzbqix","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-travel-request-jaugmtsivu","parentfield":"costings","parenttype":"Travel Request","idx":1,"__unedited":false,"expense_type":"Accommodation","custom_no_of_days":4,"total_amount":120000,"custom_cost_per_day":30000}],"total_travel_cost":120000,"purpose_of_travel":"Site Survey","employee_name":"Daudi Thomas Majinge","cell_number":"0685375814","prefered_email":"<EMAIL>","custom_department":"Technical - A","date_of_birth":"1996-07-03","passport_number":"NA","custom_hod":"","employee":"HR-EMP-00006","cost_center":"Main - A"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-08 21:24:19,278 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"docstatus":0,"doctype":"Travel Request","name":"new-travel-request-zipjpkkxge","__islocal":1,"__unsaved":1,"owner":"Administrator","travel_type":"Domestic","travel_funding":"Require Full Funding","company":"Rubis Technical Services Limited","itinerary":[{"docstatus":0,"doctype":"Travel Itinerary","name":"new-travel-itinerary-uspiqbxkzo","__islocal":1,"__unsaved":1,"owner":"Administrator","mode_of_travel":"Company Vehicle","meal_preference":"","travel_advance_required":0,"lodging_required":0,"parent":"new-travel-request-zipjpkkxge","parentfield":"itinerary","parenttype":"Travel Request","idx":1,"__unedited":false,"travel_from":"dar","travel_to":"arusha","departure_date":"2025-07-08 21:23:45"}],"costings":[{"docstatus":0,"doctype":"Travel Request Costing","name":"new-travel-request-costing-naucgtjwli","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-travel-request-zipjpkkxge","parentfield":"costings","parenttype":"Travel Request","idx":1,"__unedited":false,"expense_type":"Accommodation","custom_no_of_days":3,"total_amount":150000,"custom_cost_per_day":50000}],"total_travel_cost":150000,"purpose_of_travel":"Out of Office","employee_name":"Daudi Thomas Majinge","cell_number":"0685375814","prefered_email":"<EMAIL>","custom_department":"Technical - A","date_of_birth":"1996-07-03","passport_number":"NA","custom_hod":"<EMAIL>","employee":"HR-EMP-00006"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-09 08:55:23,302 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {}
2025-07-12 23:03:31,091 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:07:26,967 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:08:01,688 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:08:27,315 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:10:52,177 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:14:04,292 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:17:41,392 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
