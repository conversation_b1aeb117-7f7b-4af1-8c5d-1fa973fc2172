// Copyright (c) 2025, Your Company and contributors
// For license information, please see license.txt

frappe.query_reports["Register Salary"] = {
	filters: [
		{
			fieldname: "from_date",
			label: __("From"),
			fieldtype: "Date",
			default: frappe.datetime.add_months(frappe.datetime.get_today(), -1),
			reqd: 1,
			width: "100px",
		},
		{
			fieldname: "to_date",
			label: __("To"),
			fieldtype: "Date",
			default: frappe.datetime.get_today(),
			reqd: 1,
			width: "100px",
		},
		{
			fieldname: "currency",
			fieldtype: "Link",
			options: "Currency",
			label: __("Currency"),
			default: erpnext.get_currency(frappe.defaults.get_default("Company")),
			width: "50px",
		},
		{
			fieldname: "employee",
			label: __("Employee"),
			fieldtype: "Link",
			options: "Employee",
			width: "100px",
		},
		{
			fieldname: "company",
			label: __("Company"),
			fieldtype: "Link",
			options: "Company",
			default: frappe.defaults.get_user_default("Company"),
			width: "100px",
			reqd: 1,
		},
		{
			fieldname: "docstatus",
			label: __("Document Status"),
			fieldtype: "Select",
			options: ["Draft", "Submitted", "Cancelled"],
			default: "Draft",
			width: "100px",
		},
		{
			fieldname: "department",
			label: __("Department"),
			fieldtype: "Link",
			options: "Department",
			width: "100px",
			get_query: function () {
				return {
					filters: {
						company: frappe.query_report.get_filter_value("company"),
					},
				};
			},
		},
		{
			fieldname: "designation",
			label: __("Designation"),
			fieldtype: "Link",
			options: "Designation",
			width: "100px",
		},
		{
			fieldname: "branch",
			label: __("Branch"),
			fieldtype: "Link",
			options: "Branch",
			width: "100px",
		},
	],

	onload: function(report) {
		// Check workflow and add appropriate buttons
		frappe.call({
			method: "csf_tz.csf_tz.report.register_salary.register_salary.get_workflow_info",
			callback: function(r) {
				if (r.message) {
					report.workflow_info = r.message;
					setup_approval_buttons(report);
				}
			}
		});
	},

	get_datatable_options(options) {
		return Object.assign(options, {
			checkboxColumn: true,
			events: {
				onCheckRow: function() {
					// Handle row selection
				}
			}
		});
	}
};

function setup_approval_buttons(report) {
	// Clear existing buttons
	report.page.clear_inner_toolbar();

	if (report.workflow_info && report.workflow_info.has_workflow) {
		// Add workflow-based buttons
		report.page.add_inner_button(__("Approve Selected"), function() {
			approve_selected_salary_slips(report);
		}, __("Workflow Actions"));

		report.page.add_inner_button(__("Approve All Draft"), function() {
			approve_all_salary_slips(report);
		}, __("Workflow Actions"));

		report.page.add_inner_button(__("Reject Selected"), function() {
			reject_selected_salary_slips(report);
		}, __("Workflow Actions"));

		// Add workflow info button
		report.page.add_inner_button(__("Workflow Info"), function() {
			show_workflow_info(report);
		}, __("Info"));
	} else {
		// Add basic approval buttons for non-workflow scenarios
		report.page.add_inner_button(__("Submit Selected"), function() {
			approve_selected_salary_slips(report);
		}, __("Actions"));

		report.page.add_inner_button(__("Submit All Draft"), function() {
			approve_all_salary_slips(report);
		}, __("Actions"));
	}

	// Add refresh button
	report.page.add_inner_button(__("Refresh"), function() {
		report.refresh();
	}, __("Actions"));
}

function approve_selected_salary_slips(report) {
	let selected_rows = report.datatable.rowmanager.getCheckedRows();
	if (selected_rows.length === 0) {
		frappe.msgprint(__("Please select salary slips to approve"));
		return;
	}

	// Filter only actionable salary slips
	let actionable_slips = selected_rows.filter(row => {
		let slip_data = report.data[row];
		return slip_data.docstatus === 0 ||
			   (slip_data.workflow_state && slip_data.workflow_state !== "Approved");
	}).map(row => report.data[row].salary_slip_id);

	if (actionable_slips.length === 0) {
		frappe.msgprint(__("No actionable salary slips selected. Please select draft or pending approval slips."));
		return;
	}

	let action_text = report.workflow_info && report.workflow_info.has_workflow ?
		"approve" : "submit";

	frappe.confirm(
		__("Are you sure you want to {0} {1} salary slip(s)?", [action_text, actionable_slips.length]),
		function() {
			process_salary_slips(actionable_slips, "approve", report);
		}
	);
}

function approve_all_salary_slips(report) {
	if (!report.data || report.data.length === 0) {
		frappe.msgprint(__("No salary slips to approve"));
		return;
	}

	// Filter actionable salary slips based on workflow state
	let salary_slip_ids = report.data.filter(row => {
		if (report.workflow_info && report.workflow_info.has_workflow) {
			// For workflow: include draft and pending states
			return row.docstatus === 0 ||
				   (row.workflow_state &&
				    row.workflow_state !== "Approved" &&
				    row.workflow_state !== "Submitted");
		} else {
			// For non-workflow: only draft
			return row.docstatus === 0;
		}
	}).map(row => row.salary_slip_id);

	if (salary_slip_ids.length === 0) {
		let message = report.workflow_info && report.workflow_info.has_workflow ?
			__("No actionable salary slips found to approve") :
			__("No draft salary slips found to approve");
		frappe.msgprint(message);
		return;
	}

	let action_text = report.workflow_info && report.workflow_info.has_workflow ?
		"approve" : "submit";

	frappe.confirm(
		__("Are you sure you want to {0} all {1} actionable salary slip(s)?", [action_text, salary_slip_ids.length]),
		function() {
			process_salary_slips(salary_slip_ids, "approve", report);
		}
	);
}

function reject_selected_salary_slips(report) {
	let selected_rows = report.datatable.rowmanager.getCheckedRows();
	if (selected_rows.length === 0) {
		frappe.msgprint(__("Please select salary slips to reject"));
		return;
	}

	// Filter only rejectable salary slips
	let rejectable_slips = selected_rows.filter(row => {
		let slip_data = report.data[row];
		return slip_data.docstatus === 0 ||
			   (slip_data.workflow_state &&
			    slip_data.workflow_state !== "Rejected" &&
			    slip_data.workflow_state !== "Cancelled");
	}).map(row => report.data[row].salary_slip_id);

	if (rejectable_slips.length === 0) {
		frappe.msgprint(__("No rejectable salary slips selected. Please select draft or pending slips."));
		return;
	}

	frappe.prompt([
		{
			fieldname: 'reason',
			fieldtype: 'Small Text',
			label: __('Reason for Rejection'),
			reqd: 1
		}
	], function(values) {
		process_salary_slips(rejectable_slips, "reject", report, values.reason);
	}, __("Reject Salary Slips"));
}

function process_salary_slips(salary_slip_ids, action, report, reason = null) {
	frappe.call({
		method: "csf_tz.csf_tz.report.register_salary.register_salary.process_salary_slip_approval",
		args: {
			salary_slip_ids: salary_slip_ids,
			action: action,
			reason: reason
		},
		freeze: true,
		freeze_message: __("Processing salary slips..."),
		callback: function(r) {
			if (r.message) {
				frappe.msgprint(r.message);
				report.refresh();
			}
		}
	});
}

function show_workflow_info(report) {
	if (!report.workflow_info || !report.workflow_info.has_workflow) {
		frappe.msgprint(__("No workflow configured for Salary Slip"));
		return;
	}

	let workflow_info = report.workflow_info;
	let html = `
		<div class="workflow-info">
			<h4>${__("Workflow Information")}</h4>
			<p><strong>${__("Workflow Name")}:</strong> ${workflow_info.workflow_name}</p>

			<h5>${__("Available States")}:</h5>
			<ul>
				${workflow_info.states.map(state => `<li>${state}</li>`).join('')}
			</ul>

			<h5>${__("Workflow Transitions")}:</h5>
			<table class="table table-bordered">
				<thead>
					<tr>
						<th>${__("From State")}</th>
						<th>${__("Action")}</th>
						<th>${__("To State")}</th>
						<th>${__("Allowed Role")}</th>
					</tr>
				</thead>
				<tbody>
					${workflow_info.transitions.map(t => `
						<tr>
							<td>${t.state}</td>
							<td>${t.action}</td>
							<td>${t.next_state}</td>
							<td>${t.allowed}</td>
						</tr>
					`).join('')}
				</tbody>
			</table>
		</div>
	`;

	frappe.msgprint({
		title: __("Workflow Information"),
		message: html,
		wide: true
	});
}
